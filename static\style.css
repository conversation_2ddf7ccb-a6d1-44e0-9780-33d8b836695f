/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #0f0f0f;
    color: #fff;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header styles */
.header {
    background-color: #212121;
    border-bottom: 1px solid #3d3d3d;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    font-size: 32px;
    color: #ff0000;
}

h1 {
    font-size: 24px;
    font-weight: 500;
    color: #fff;
}

/* Form styles */
.add-video-form {
    flex: 1;
    max-width: 500px;
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

.video-input {
    flex: 1;
    padding: 12px 16px;
    background-color: #121212;
    border: 2px solid #3d3d3d;
    border-radius: 12px;
    color: #fff;
    font-size: 14px;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 48px;
    font-family: inherit;
    line-height: 1.4;
}

.video-input:focus {
    outline: none;
    border-color: #065fd4;
    background-color: #1a1a1a;
}

.video-input::placeholder {
    color: #aaa;
}

.add-button {
    padding: 12px 20px;
    background-color: #065fd4;
    color: #fff;
    border: none;
    border-radius: 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.add-button:hover {
    background-color: #0856c7;
    transform: translateY(-1px);
}

.add-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Main content */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    width: 100%;
}

/* Controls section */
.controls-section {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

/* Sorting controls */
.sorting-controls {
    display: flex;
    align-items: center;
}

/* Filter Controls - Grouped Videos Toggle */
.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-form {
    display: flex;
    align-items: center;
}

/* Toggle Switch Styling */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background-color: #ccc;
    border-radius: 24px;
    transition: background-color 0.3s;
}

.toggle-slider:before {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider {
    background-color: #ff0000;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.toggle-label .material-symbols-outlined {
    font-size: 18px;
}

.sort-form {
    display: flex;
    align-items: center;
    gap: 12px;
}

.sort-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #aaa;
    font-size: 14px;
    font-weight: 500;
}

.sort-dropdown {
    background-color: #212121;
    color: #fff;
    border: 2px solid #3d3d3d;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 180px;
}

.sort-dropdown:focus {
    outline: none;
    border-color: #065fd4;
    background-color: #2a2a2a;
}

.sort-dropdown:hover {
    border-color: #555;
}

.sort-dropdown option {
    background-color: #212121;
    color: #fff;
    padding: 8px;
}

/* Action controls */
.action-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Export menu */
.export-menu {
    position: relative;
    display: inline-block;
}

.export-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #2a2a2a;
    color: #fff;
    border: 2px solid #3d3d3d;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.export-button:hover {
    background-color: #3d3d3d;
    border-color: #555;
}

.export-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #2a2a2a;
    border: 2px solid #3d3d3d;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 180px;
    margin-top: 4px;
}

.export-dropdown.show {
    display: block;
}

.export-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.export-option:hover {
    background-color: #3d3d3d;
}

.export-option:first-child {
    border-radius: 6px 6px 0 0;
}

.export-option:last-child {
    border-radius: 0 0 6px 6px;
}

/* Import menu */
.import-menu {
    position: relative;
    display: inline-block;
}

.import-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #2a2a2a;
    color: #fff;
    border: 2px solid #3d3d3d;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.import-button:hover {
    background-color: #3d3d3d;
    border-color: #555;
}

.import-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #2a2a2a;
    border: 1px solid #3d3d3d;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 250px;
    padding: 12px;
}

.import-dropdown.show {
    display: block;
}

.import-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.import-option {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: #3d3d3d;
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.file-label:hover {
    background-color: #555;
}

.file-input {
    display: none;
}

.file-name {
    font-size: 12px;
    color: #aaa;
    padding: 4px 8px;
    background-color: #1a1a1a;
    border-radius: 4px;
    text-align: center;
}

.import-submit-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #065fd4;
    color: #fff;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.import-submit-button:hover {
    background-color: #0a7cff;
}

.refresh-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #065fd4;
    color: #fff;
    text-decoration: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.refresh-button:hover {
    background-color: #0a7cff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(6, 95, 212, 0.3);
}

.refresh-button:active {
    transform: translateY(0);
}

/* Main layout */
.main-layout {
    display: flex;
    gap: 24px;
    margin-top: 24px;
}

/* Group sidebar */
.group-sidebar {
    width: 280px;
    background-color: #212121;
    border-radius: 12px;
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 24px;
}

.sidebar-header {
    margin-bottom: 20px;
}

.sidebar-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
}

/* Create group form */
.create-group-form {
    margin-bottom: 20px;
}

.input-group {
    display: flex;
    gap: 8px;
}

.group-input {
    flex: 1;
    padding: 8px 12px;
    background-color: #121212;
    border: 2px solid #3d3d3d;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.group-input:focus {
    outline: none;
    border-color: #065fd4;
}

.group-input::placeholder {
    color: #aaa;
}

.create-group-button {
    padding: 8px 12px;
    background-color: #065fd4;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.create-group-button:hover {
    background-color: #0a7cff;
}

/* Group list */
.group-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.group-item-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.group-item {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #aaa;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.group-item:hover {
    background-color: #2a2a2a;
    color: #fff;
}

.group-item.active {
    background-color: #065fd4;
    color: #fff;
}

.group-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.video-count {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    min-width: 20px;
    text-align: center;
}

.delete-group-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: transparent;
    color: #666;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.delete-group-button:hover {
    background-color: #ff4444;
    color: #fff;
}

/* Video content */
.video-content {
    flex: 1;
    min-width: 0;
}

/* Video grid */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

/* Video card styles */
.video-card {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.video-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.thumbnail-container {
    position: relative;
    overflow: hidden;
}

.thumbnail {
    width: 100%;
    aspect-ratio: 16 / 9;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .thumbnail {
    transform: scale(1.05);
}

.video-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.video-card:hover .video-actions {
    opacity: 1;
}

.group-manage-button,
.remove-button {
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 18px;
}

.group-manage-button:hover {
    background-color: #065fd4;
    transform: scale(1.1);
}

.remove-button:hover {
    background-color: #ff0000;
    transform: scale(1.1);
}

/* Special styling for cancel button on pending videos */
.remove-button.cancel-button {
    width: auto;
    padding: 6px 12px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: rgba(255, 152, 0, 0.9);
}

.remove-button.cancel-button:hover {
    background-color: #ff9800;
    transform: scale(1.05);
}

.remove-button.cancel-button .button-text {
    font-size: 12px;
    font-weight: 500;
}

/* Video length badge */
.video-length-badge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    backdrop-filter: blur(4px);
}

/* Video info */
.video-info {
    padding: 12px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.channel-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    background-color: #3d3d3d;
}

.video-text {
    flex: 1;
    min-width: 0;
}

.video-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.3;
    margin: 0 0 4px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-title a {
    color: #fff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.video-title a:hover {
    color: #065fd4;
}

.channel-name {
    font-size: 13px;
    color: #aaa;
    font-weight: 400;
    margin: 0 0 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.channel-link {
    color: #aaa;
    text-decoration: none;
    transition: color 0.2s ease;
}

.channel-link:hover {
    color: #065fd4;
    text-decoration: none;
}

.video-meta {
    font-size: 13px;
    color: #aaa;
    font-weight: 400;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Group Pills */
.group-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin: 4px 0 2px 0;
}

.group-link {
    text-decoration: none;
    display: inline-block;
}

.group-pill {
    background-color: #065fd4;
    color: #fff;
    font-size: 11px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 10px;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.group-link:hover .group-pill {
    background-color: #0f7ae5;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(6, 95, 212, 0.3);
}

/* Empty state */
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    text-align: center;
}

.empty-content {
    max-width: 500px;
}

.empty-icon {
    font-size: 64px;
    color: #3d3d3d;
    margin-bottom: 16px;
}

.empty-content h2 {
    font-size: 24px;
    margin-bottom: 8px;
    color: #fff;
}

.empty-content p {
    color: #aaa;
    margin-bottom: 24px;
}

.example-urls {
    background-color: #1a1a1a;
    padding: 20px;
    border-radius: 8px;
    text-align: left;
}

.example-urls p {
    margin-bottom: 12px;
    color: #fff;
}

.example-urls ul {
    list-style: none;
    padding-left: 0;
}

.example-urls li {
    color: #aaa;
    margin-bottom: 8px;
    padding-left: 16px;
    position: relative;
}

.example-urls li::before {
    content: "•";
    color: #065fd4;
    position: absolute;
    left: 0;
}

/* Footer */
.footer {
    background-color: #212121;
    border-top: 1px solid #3d3d3d;
    padding: 16px 24px;
    text-align: center;
    color: #aaa;
    font-size: 14px;
    margin-top: auto;
}

/* Responsive design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
        padding: 16px;
    }
    
    .input-container {
        flex-direction: column;
        width: 100%;
    }
    
    .video-input {
        width: 100%;
    }

    .controls-section {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 16px;
    }

    .sorting-controls {
        justify-content: center;
    }

    .sort-form {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .sort-dropdown {
        min-width: 200px;
    }

    .action-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .main-layout {
        flex-direction: column;
        gap: 16px;
    }

    .group-sidebar {
        width: 100%;
        position: static;
    }

    .video-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .main-content {
        padding: 16px;
    }

    h1 {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .logo-section {
        justify-content: center;
    }

    .add-button {
        width: 100%;
        justify-content: center;
    }

    .main-layout {
        flex-direction: column;
    }

    .group-sidebar {
        width: 100%;
        position: static;
    }
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: #2a2a2a;
    margin: 10% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #3d3d3d;
}

.modal-header h3 {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: #aaa;
    font-size: 24px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #3d3d3d;
    color: #fff;
}

.modal-body {
    padding: 24px;
}

.group-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.checkbox-item:hover {
    background-color: #3d3d3d;
}

.group-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #065fd4;
}

.checkbox-label {
    color: #fff;
    font-size: 14px;
    flex: 1;
}

.no-groups {
    color: #aaa;
    text-align: center;
    font-style: italic;
    margin: 20px 0;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #3d3d3d;
}

.cancel-button,
.save-button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button {
    background-color: #3d3d3d;
    color: #fff;
}

.cancel-button:hover {
    background-color: #555;
}

.save-button {
    background-color: #065fd4;
    color: #fff;
}

.save-button:hover {
    background-color: #0a7cff;
}

/* Channels Page Styles */
.nav-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: transparent;
    color: #aaa;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-button:hover {
    background-color: #3d3d3d;
    color: #fff;
}

.nav-button.active {
    background-color: #065fd4;
    color: #fff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 500;
    margin: 0;
    color: #fff;
}

.channels-content {
    margin-top: 24px;
}

.channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    padding: 0;
}

.channel-card {
    background-color: #1a1a1a;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid #3d3d3d;
}

.channel-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: #555;
}

.channel-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.channel-logo {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    background-color: #3d3d3d;
    flex-shrink: 0;
}

.channel-info {
    flex: 1;
    min-width: 0;
}

.channel-name {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.channel-name a {
    color: #fff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.channel-name a:hover {
    color: #065fd4;
}

.channel-subscribers {
    font-size: 14px;
    color: #aaa;
    margin: 0;
    font-weight: 400;
}

.channel-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
    padding: 12px 0;
    border-top: 1px solid #3d3d3d;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #aaa;
}

.stat-item .material-symbols-outlined {
    font-size: 16px;
    color: #666;
}

.stat-value {
    font-weight: 400;
}

.channel-actions {
    display: flex;
    gap: 8px;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: #065fd4;
    color: #fff;
    text-decoration: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex: 1;
    justify-content: center;
}

.action-button:hover {
    background-color: #0a7cff;
}

.empty-action-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background-color: #065fd4;
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-top: 16px;
}

.empty-action-button:hover {
    background-color: #0a7cff;
}

/* ===================================================================== */
/* ASYNCHRONOUS VIDEO STATES STYLES - Professional Loading & Error UI */
/* ===================================================================== */

/* Pending Video Card */
.video-card-pending {
    opacity: 0.8;
    border: 2px dashed #555;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    position: relative;
    overflow: hidden;
}

.video-card-pending::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.thumbnail-placeholder {
    position: relative;
}

.thumbnail-loading {
    filter: grayscale(50%) brightness(0.7);
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #333;
    border-top: 3px solid #065fd4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid #333;
    border-top: 2px solid #065fd4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-badge {
    background: #555 !important;
    color: #aaa !important;
}

.channel-icon-placeholder {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.loading-title {
    color: #888 !important;
    position: relative;
}

.loading-text {
    opacity: 0.7;
}

.loading-dots {
    display: inline-block;
    margin-left: 4px;
}

.loading-dots span {
    animation: blink 1.4s infinite both;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes blink {
    0%, 80%, 100% { opacity: 0; }
    40% { opacity: 1; }
}

.loading-channel, .loading-meta {
    color: #666 !important;
    opacity: 0.8;
}

/* Error Video Card */
.video-card-error {
    opacity: 0.9;
    border: 2px solid #d32f2f;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a1a1a 100%);
}

.thumbnail-error {
    position: relative;
}

.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(211, 47, 47, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.error-icon {
    font-size: 32px !important;
    color: #fff;
}

.error-badge {
    background: #d32f2f !important;
    color: #fff !important;
}

.error-icon-placeholder {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #d32f2f;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.error-icon-placeholder .material-symbols-outlined {
    font-size: 20px;
    color: #fff;
}

.error-title {
    color: #d32f2f !important;
}

.error-channel, .error-meta {
    color: #d32f2f !important;
    opacity: 0.8;
}

/**
 * Real-time Progress System for Background Scraping Jobs
 * 
 * Provides accurate progress feedback by polling the server for actual
 * background job status and updating the progress bar accordingly.
 */

class RealtimeProgressTracker {
    constructor() {
        this.isPolling = false;
        this.pollInterval = null;
        this.initialPendingCount = 0;
        this.pollIntervalMs = 2500; // Poll every 2.5 seconds
        this.maxPollDuration = 300000; // Stop polling after 5 minutes
        this.startTime = null;
    }

    /**
     * Start tracking progress for background scraping jobs
     */
    startTracking() {
        if (this.isPolling) {
            console.log('📊 Progress tracking already active');
            return;
        }

        console.log('📊 Starting real-time progress tracking...');
        this.isPolling = true;
        this.startTime = Date.now();
        
        // Get initial status
        this.checkStatus().then(status => {
            if (status.pending_count > 0) {
                this.initialPendingCount = status.pending_count;
                console.log(`📊 Initial pending jobs: ${this.initialPendingCount}`);
                
                // Start the progress bar
                NProgress.start();
                
                // Begin polling
                this.startPolling();
                
                // Show initial status message
                this.showStatusMessage(`Scraping ${this.initialPendingCount} videos...`);
            } else {
                console.log('📊 No pending jobs found');
                this.stopTracking();
            }
        }).catch(error => {
            console.error('📊 Error starting progress tracking:', error);
            this.stopTracking();
        });
    }

    /**
     * Start the polling interval
     */
    startPolling() {
        this.pollInterval = setInterval(() => {
            this.checkStatus().then(status => {
                this.updateProgress(status);
                
                // Stop if no more pending jobs
                if (status.pending_count === 0) {
                    this.completeTracking();
                }
                
                // Stop if polling too long (safety)
                if (Date.now() - this.startTime > this.maxPollDuration) {
                    console.log('📊 Polling timeout reached, stopping');
                    this.stopTracking();
                }
            }).catch(error => {
                console.error('📊 Polling error:', error);
                this.stopTracking();
            });
        }, this.pollIntervalMs);
    }

    /**
     * Check scraping status from server
     */
    async checkStatus() {
        const response = await fetch('/scraping_status');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return await response.json();
    }

    /**
     * Update progress bar based on current status
     */
    updateProgress(status) {
        if (this.initialPendingCount === 0) return;
        
        const completed = this.initialPendingCount - status.pending_count;
        const progress = Math.min(completed / this.initialPendingCount, 0.95); // Cap at 95% until complete
        
        console.log(`📊 Progress: ${completed}/${this.initialPendingCount} (${Math.round(progress * 100)}%)`);
        
        // Update progress bar
        NProgress.set(progress);
        
        // Update status message
        if (status.pending_count > 0) {
            this.showStatusMessage(`Scraping ${status.pending_count} videos...`);
        }
    }

    /**
     * Complete the tracking process
     */
    completeTracking() {
        console.log('📊 All scraping jobs completed!');
        
        // Complete the progress bar
        NProgress.done();
        
        // Show completion message
        this.showCompletionToast();
        
        // Stop tracking
        this.stopTracking();
        
        // Refresh the page after a short delay to show updated data
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }

    /**
     * Stop progress tracking
     */
    stopTracking() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
        
        this.isPolling = false;
        this.initialPendingCount = 0;
        this.startTime = null;
        
        console.log('📊 Progress tracking stopped');
    }

    /**
     * Show status message
     */
    showStatusMessage(message) {
        // Update any existing status elements
        const statusElements = document.querySelectorAll('.scraping-status');
        statusElements.forEach(el => {
            el.textContent = message;
        });
    }

    /**
     * Show completion toast notification
     */
    showCompletionToast() {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'completion-toast';
        toast.innerHTML = `
            <div class="toast-content">
                <span class="material-symbols-outlined">check_circle</span>
                <span>Scraping Complete!</span>
            </div>
        `;
        
        // Add toast styles
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            animation: slideInRight 0.3s ease-out;
        `;
        
        // Add animation keyframes
        if (!document.querySelector('#toast-animations')) {
            const style = document.createElement('style');
            style.id = 'toast-animations';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add to page
        document.body.appendChild(toast);
        
        // Remove after delay
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
}

// Create global instance
window.realtimeProgress = new RealtimeProgressTracker();

// Auto-start tracking when page loads if there are pending videos
document.addEventListener('DOMContentLoaded', function() {
    // Check if there are pending videos on the page
    const pendingVideos = document.querySelectorAll('.video-card[data-status="pending"]');
    
    if (pendingVideos.length > 0) {
        console.log(`📊 Found ${pendingVideos.length} pending videos, starting progress tracking`);
        
        // Start tracking after a short delay
        setTimeout(() => {
            window.realtimeProgress.startTracking();
        }, 1000);
    }
});

// Also start tracking when new videos are added
document.addEventListener('submit', function(event) {
    const form = event.target;
    
    // Check if this is the add video form
    if (form.action && form.action.includes('/add_video')) {
        console.log('📊 Video add form submitted, will start tracking after page reload');
        
        // The tracking will start automatically on the next page load
        // if there are pending videos
    }
});

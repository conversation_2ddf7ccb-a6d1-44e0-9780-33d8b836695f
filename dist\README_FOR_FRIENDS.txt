🎥 YouTube Competitor Analysis Tool - User Guide
================================================

Hey! Here's how to use the YouTube dashboard tool:

📋 QUICK START:
1. Double-click "app.exe" to run the application
2. When Windows Firewall asks, click "Allow access" 
3. Open your web browser (Chrome, Firefox, Edge, etc.)
4. Go to: http://127.0.0.1:5000
5. That's it! The tool is now running in your browser

🔧 WHAT THIS TOOL DOES:
- Track YouTube videos and analyze competitor content
- Organize videos into groups for easy management
- Export data to CSV or JSON formats
- Track channel statistics and subscriber counts
- No API keys required - uses web scraping

💡 HOW TO USE:
- Add videos: Paste YouTube video URLs in the input box
- Create groups: Use the "Create Group" button to organize videos
- Sort & filter: Use the dropdown menus to organize your view
- Export data: Click "Export" to download your data
- Add channels: Go to "Channels" tab to track channel stats

🛑 HOW TO STOP:
- Close your browser tab
- The app will continue running in the background
- To fully stop: Look for the app icon in your system tray (bottom-right)
- Right-click the icon and choose "Exit" or "Quit"
- Or use Task Manager to end the "app.exe" process

⚠️ IMPORTANT NOTES:
- This is a standalone application - no installation needed
- Your data is stored locally on your computer
- The app runs a local web server (only accessible from your computer)
- If antivirus software flags it, it's a false positive - the app is safe
- Keep this folder together - don't separate the .exe from other files

🆘 TROUBLESHOOTING:
- If the browser shows "can't connect": Make sure app.exe is running
- If Windows blocks it: Right-click app.exe → Properties → Unblock
- If it won't start: Try running as administrator
- If port 5000 is busy: Close other applications using that port

📧 Questions? Contact the person who shared this tool with you!

Enjoy analyzing YouTube content! 🚀

import os
import json
import re
import requests
from bs4 import BeautifulSoup
from flask import Flask, render_template, request, redirect, url_for, jsonify, Response
import time
from datetime import datetime, timezone
import csv
import io
import uuid

# --- CONFIGURATION ---
VIDEO_DATA_FILE = 'videos.json'
CHANNEL_DATA_FILE = 'channels.json'

# Initialize the Flask app
app = Flask(__name__)

# --- HELPER FUNCTIONS ---

def get_video_id(youtube_url):
    """Extracts the YouTube video ID from a URL."""
    patterns = [
        r'(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=)?(?:embed\/)?(?:v\/)?(?:shorts\/)?([a-zA-Z0-9_-]{11})',
        r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})',
        r'(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, youtube_url)
        if match:
            return match.group(1)
    return None

def load_data():
    """Loads the complete data structure (videos and groups) from JSON file."""
    if not os.path.exists(VIDEO_DATA_FILE):
        return {'videos': [], 'groups': []}
    try:
        with open(VIDEO_DATA_FILE, 'r') as f:
            data = json.load(f)
            # Handle migration from old formats
            if 'video_ids' in data:
                print("Migrating from old video_ids format to new structure...")
                return {'videos': [], 'groups': []}
            elif isinstance(data.get('videos'), list) and 'groups' not in data:
                print("Migrating from cache-only format to groups structure...")
                # Migrate existing videos to new format
                videos = data.get('videos', [])
                for video in videos:
                    if 'groups' not in video:
                        video['groups'] = []  # Add empty groups list
                return {'videos': videos, 'groups': []}
            return {
                'videos': data.get('videos', []),
                'groups': data.get('groups', [])
            }
    except (json.JSONDecodeError, FileNotFoundError):
        return {'videos': [], 'groups': []}

def save_data(data):
    """Saves the complete data structure to JSON file."""
    with open(VIDEO_DATA_FILE, 'w') as f:
        json.dump(data, f, indent=2)

def load_video_cache():
    """Backward compatibility function - loads just videos."""
    data = load_data()
    return data['videos']

def save_video_cache(videos):
    """Backward compatibility function - saves just videos."""
    data = load_data()
    data['videos'] = videos
    save_data(data)

def find_video_by_id(videos, video_id):
    """Finds a video object by its ID in the cache."""
    for video in videos:
        if video.get('id') == video_id:
            return video
    return None

def find_group_by_id(groups, group_id):
    """Finds a group object by its ID."""
    for group in groups:
        if group.get('id') == group_id:
            return group
    return None

def create_group(name):
    """Creates a new group object with unique ID."""
    return {
        'id': str(uuid.uuid4()),
        'name': name,
        'created': datetime.now(timezone.utc).isoformat()
    }

def filter_videos_by_group(videos, group_id):
    """Filters videos to only include those in the specified group."""
    if not group_id or group_id == 'all':
        return videos
    return [video for video in videos if group_id in video.get('groups', [])]

def format_view_count(view_count_str):
    """Formats the view count into a human-readable string."""
    try:
        # Remove commas and convert to int
        view_count = int(view_count_str.replace(',', '').replace(' views', ''))

        if view_count >= 1_000_000_000:
            return f"{view_count / 1_000_000_000:.1f}B views"
        elif view_count >= 1_000_000:
            return f"{view_count / 1_000_000:.1f}M views"
        elif view_count >= 1_000:
            return f"{view_count / 1_000:.1f}K views"
        else:
            return f"{view_count:,} views"
    except (ValueError, AttributeError):
        return view_count_str

def format_duration(seconds_str):
    """Converts duration from seconds to MM:SS format."""
    try:
        total_seconds = int(seconds_str)
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes}:{seconds:02d}"
    except (ValueError, TypeError):
        return "0:00"

def calculate_time_ago(date_str):
    """Calculates relative time from a date string (YYYY-MM-DD format)."""
    try:
        # Parse the date string
        upload_date = datetime.strptime(date_str, "%Y-%m-%d")
        # Make it timezone aware (assume UTC)
        upload_date = upload_date.replace(tzinfo=timezone.utc)

        # Get current time in UTC
        now = datetime.now(timezone.utc)

        # Calculate the difference
        diff = now - upload_date

        # Convert to total seconds for easier calculation
        total_seconds = int(diff.total_seconds())

        # Calculate time units
        if total_seconds < 60:
            return "just now"
        elif total_seconds < 3600:  # Less than 1 hour
            minutes = total_seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif total_seconds < 86400:  # Less than 1 day
            hours = total_seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif total_seconds < 604800:  # Less than 1 week
            days = total_seconds // 86400
            return f"{days} day{'s' if days != 1 else ''} ago"
        elif total_seconds < 2629746:  # Less than 1 month (30.44 days average)
            weeks = total_seconds // 604800
            return f"{weeks} week{'s' if weeks != 1 else ''} ago"
        elif total_seconds < 31556952:  # Less than 1 year (365.24 days)
            months = total_seconds // 2629746
            return f"{months} month{'s' if months != 1 else ''} ago"
        else:
            years = total_seconds // 31556952
            return f"{years} year{'s' if years != 1 else ''} ago"

    except (ValueError, TypeError):
        return ""

def scrape_youtube_video_to_object(video_id):
    """
    Scrapes video details from YouTube and returns a complete video object for caching.
    This is the ONLY function that should make network requests.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    url = f"https://www.youtube.com/watch?v={video_id}"
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # ENHANCED 3-TIER WATERFALL DATE SCRAPING SYSTEM
        soup = BeautifulSoup(response.text, 'html.parser')
        precise_date = ""
        formatted_date = "Unknown"
        time_ago = ""
        publish_date_raw = None

        def parse_date_string(date_str, source="unknown"):
            """Helper function to parse various date formats and return standardized data."""
            if not date_str:
                return None, None, None

            # Try multiple date formats
            date_formats = [
                "%Y-%m-%d",           # 2023-05-14
                "%b %d, %Y",          # May 14, 2023
                "%d %b %Y",           # 14 May 2023
                "%Y-%m-%dT%H:%M:%S",  # 2023-05-14T10:30:00
                "%Y-%m-%dT%H:%M:%SZ", # 2023-05-14T10:30:00Z
            ]

            for date_format in date_formats:
                try:
                    date_obj = datetime.strptime(date_str.strip(), date_format)
                    formatted = date_obj.strftime("%b %d, %Y")
                    time_ago_calc = calculate_time_ago(date_obj.strftime("%Y-%m-%d"))
                    print(f"Successfully parsed date from {source}: {date_str} -> {formatted}")
                    return date_obj, formatted, time_ago_calc
                except ValueError:
                    continue
            return None, None, None

        # ATTEMPT 1 (PRIMARY): Parse ytInitialPlayerResponse JSON for most reliable date
        player_response_match = re.search(r'var ytInitialPlayerResponse = ({.*?});', response.text)
        if player_response_match and not publish_date_raw:
            try:
                data = json.loads(player_response_match.group(1))

                # Try videoDetails.publishDate first
                video_details = data.get('videoDetails', {})
                if 'publishDate' in video_details:
                    date_obj, formatted, time_ago_calc = parse_date_string(video_details['publishDate'], "ytInitialPlayerResponse.videoDetails.publishDate")
                    if date_obj:
                        publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc

                # Try microformat.playerMicroformatRenderer.publishDate
                if not publish_date_raw:
                    microformat = data.get('microformat', {}).get('playerMicroformatRenderer', {})
                    if 'publishDate' in microformat:
                        date_obj, formatted, time_ago_calc = parse_date_string(microformat['publishDate'], "microformat.publishDate")
                        if date_obj:
                            publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc

                # Try uploadDate as well
                if not publish_date_raw and 'uploadDate' in microformat:
                    date_obj, formatted, time_ago_calc = parse_date_string(microformat['uploadDate'], "microformat.uploadDate")
                    if date_obj:
                        publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc

            except (json.JSONDecodeError, KeyError, IndexError) as e:
                print(f"Failed to parse ytInitialPlayerResponse for date: {e}")

        # ATTEMPT 2 (FALLBACK): Look for the datePublished meta tag
        if not publish_date_raw:
            date_meta = soup.find('meta', {'itemprop': 'datePublished'})
            if date_meta:
                date_content = date_meta.get('content', '')
                date_obj, formatted, time_ago_calc = parse_date_string(date_content, "meta[itemprop='datePublished']")
                if date_obj:
                    publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc

        # ATTEMPT 3 (FINAL FALLBACK): Regex search for date patterns in HTML
        if not publish_date_raw:
            print("Attempting regex fallback for date extraction...")
            # Common date patterns to search for
            date_patterns = [
                r'"publishDate":"([^"]+)"',                    # JSON publishDate
                r'"datePublished":"([^"]+)"',                  # JSON datePublished
                r'datePublished["\s]*:["\s]*([^"]+)',         # Various JSON formats
                r'uploadDate["\s]*:["\s]*([^"]+)',            # uploadDate variants
                r'Published on ([A-Za-z]+ \d{1,2}, \d{4})',   # "Published on May 14, 2023"
                r'(\d{4}-\d{2}-\d{2})',                       # YYYY-MM-DD anywhere
                r'([A-Za-z]+ \d{1,2}, \d{4})',               # "May 14, 2023" format
            ]

            for pattern in date_patterns:
                matches = re.findall(pattern, response.text)
                for match in matches:
                    date_obj, formatted, time_ago_calc = parse_date_string(match, f"regex pattern: {pattern}")
                    if date_obj:
                        publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc
                        break
                if publish_date_raw:  # Break outer loop if date found
                    break

        # Now extract other video data using ytInitialPlayerResponse
        player_response_match = re.search(r'var ytInitialPlayerResponse = ({.*?});', response.text)
        channel_icon_url = ""
        title = ""
        view_count_raw = 0
        view_count = "0"
        channel_name = "Unknown Channel"
        length_seconds = "0"

        if player_response_match:
            try:
                data = json.loads(player_response_match.group(1))
                video_details = data.get('videoDetails', {})

                title = video_details.get('title', '')
                view_count_raw = int(video_details.get('viewCount', '0'))
                view_count = video_details.get('viewCount', '0')
                channel_name = video_details.get('author', 'Unknown Channel')
                length_seconds = video_details.get('lengthSeconds', '0')

                # Try to find channel icon
                initial_data_match = re.search(r'var ytInitialData = ({.*?});', response.text)
                if initial_data_match:
                    try:
                        initial_data = json.loads(initial_data_match.group(1))

                        # Try to find channel icon
                        secondary_results = initial_data.get('contents', {}).get('twoColumnWatchNextResults', {}).get('results', {}).get('results', {}).get('contents', [])
                        for content in secondary_results:
                            if 'videoSecondaryInfoRenderer' in content:
                                owner = content['videoSecondaryInfoRenderer'].get('owner', {}).get('videoOwnerRenderer', {})
                                thumbnails = owner.get('thumbnail', {}).get('thumbnails', [])
                                if thumbnails:
                                    channel_icon_url = thumbnails[-1].get('url', '')
                                break

                    except (json.JSONDecodeError, KeyError, IndexError):
                        pass

                if title and view_count:
                    # Create complete video object for caching
                    now = datetime.now(timezone.utc)
                    return {
                        'id': video_id,
                        'title': title,
                        'views': format_view_count(view_count),
                        'viewCountRaw': view_count_raw,
                        'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
                        'url': url,
                        'channelName': channel_name,
                        'channelIconUrl': channel_icon_url or f'https://i.ytimg.com/vi/{video_id}/default.jpg',
                        'uploadDate': formatted_date,
                        'publishDateRaw': publish_date_raw.isoformat() if publish_date_raw else None,
                        'timeAgo': time_ago,
                        'length': format_duration(length_seconds),
                        'groups': [],  # Initialize empty groups list
                        'dateAdded': now.isoformat(),
                        'lastUpdated': now.isoformat()
                    }
            except json.JSONDecodeError:
                pass
        
        # Method 2: Parse HTML with BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Try to find title
        title = None
        title_meta = soup.find('meta', property='og:title')
        if title_meta:
            title = title_meta.get('content')
        
        if not title:
            title_tag = soup.find('title')
            if title_tag:
                title = title_tag.text.replace(' - YouTube', '')
        
        # Try to find view count in various ways
        view_count = "0 views"
        
        # Look for view count in script tags
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                view_match = re.search(r'"viewCount":"(\d+)"', script.string)
                if view_match:
                    view_count = format_view_count(view_match.group(1))
                    break
                
                # Alternative pattern
                view_match = re.search(r'viewCount.*?(\d{1,3}(?:,\d{3})*)', script.string)
                if view_match:
                    view_count = format_view_count(view_match.group(1))
                    break
        
        # Fallback object creation
        now = datetime.now(timezone.utc)
        return {
            'id': video_id,
            'title': title or f'Video {video_id}',
            'views': view_count,
            'viewCountRaw': 0,
            'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
            'url': url,
            'channelName': 'Unknown Channel',
            'channelIconUrl': f'https://i.ytimg.com/vi/{video_id}/default.jpg',
            'uploadDate': formatted_date or 'Unknown',
            'publishDateRaw': publish_date_raw.isoformat() if publish_date_raw else None,
            'timeAgo': time_ago,
            'length': '0:00',
            'groups': [],  # Initialize empty groups list
            'dateAdded': now.isoformat(),
            'lastUpdated': now.isoformat()
        }

    except Exception as e:
        print(f"Error scraping video {video_id}: {e}")
        now = datetime.now(timezone.utc)
        return {
            'id': video_id,
            'title': f'Video {video_id}',
            'views': 'Views unavailable',
            'viewCountRaw': 0,
            'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
            'url': url,
            'channelName': 'Unknown Channel',
            'channelIconUrl': f'https://i.ytimg.com/vi/{video_id}/default.jpg',
            'uploadDate': 'Unknown',
            'publishDateRaw': None,
            'timeAgo': '',
            'length': '0:00',
            'groups': [],  # Initialize empty groups list
            'dateAdded': now.isoformat(),
            'lastUpdated': now.isoformat()
        }

def update_video_time_ago(video):
    """Updates the timeAgo field for a cached video object based on its publishDateRaw."""
    if video.get('publishDateRaw'):
        try:
            # Parse the ISO format date
            publish_date = datetime.fromisoformat(video['publishDateRaw'].replace('Z', '+00:00'))
            date_str = publish_date.strftime("%Y-%m-%d")
            video['timeAgo'] = calculate_time_ago(date_str)
        except (ValueError, TypeError):
            video['timeAgo'] = ''
    else:
        video['timeAgo'] = ''
    return video

def prepare_videos_for_display(videos, groups=None):
    """Prepares cached video objects for display by updating time-sensitive fields and adding group names."""
    display_videos = []

    # Create group lookup map for efficient group name resolution
    group_map = {}
    if groups:
        group_map = {group['id']: group['name'] for group in groups}

    for video in videos:
        # Create a copy to avoid modifying the original cache
        display_video = video.copy()
        # Update the relative time
        display_video = update_video_time_ago(display_video)

        # Add group names for display on cards
        if group_map:
            display_video['groupNames'] = [
                group_map[group_id] for group_id in video.get('groups', [])
                if group_id in group_map
            ]
        else:
            display_video['groupNames'] = []

        display_videos.append(display_video)
    return display_videos

# --- CHANNEL DATA FUNCTIONS ---

def load_channel_data():
    """Loads the channel data cache from JSON file."""
    if not os.path.exists(CHANNEL_DATA_FILE):
        return {}
    try:
        with open(CHANNEL_DATA_FILE, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}

def save_channel_data(channel_data):
    """Saves the channel data cache to JSON file."""
    with open(CHANNEL_DATA_FILE, 'w') as f:
        json.dump(channel_data, f, indent=2)

def find_channel_by_name(channel_data, channel_name):
    """Finds a channel object by its name in the cache."""
    for channel_id, channel in channel_data.items():
        if channel.get('name') == channel_name:
            return channel
    return None

def is_channel_data_stale(channel, max_age_hours=24):
    """Checks if channel data is older than max_age_hours and needs refreshing."""
    if not channel.get('lastUpdated'):
        return True

    try:
        last_updated = datetime.fromisoformat(channel['lastUpdated'].replace('Z', '+00:00'))
        now = datetime.now(timezone.utc)
        age_hours = (now - last_updated).total_seconds() / 3600
        return age_hours > max_age_hours
    except (ValueError, TypeError):
        return True

def get_unique_channels_from_videos(videos):
    """Extracts unique channel names from video data."""
    channels = set()
    for video in videos:
        channel_name = video.get('channelName')
        if channel_name and channel_name != 'Unknown Channel':
            channels.add(channel_name)
    return list(channels)

def extract_channel_id_from_url(channel_url):
    """Extracts channel ID from various YouTube channel URL formats."""
    # Handle different channel URL formats
    patterns = [
        r'youtube\.com/channel/([^/?]+)',           # /channel/UC...
        r'youtube\.com/c/([^/?]+)',                 # /c/channelname
        r'youtube\.com/@([^/?]+)',                  # /@channelname
        r'youtube\.com/user/([^/?]+)',              # /user/username
    ]

    for pattern in patterns:
        match = re.search(pattern, channel_url)
        if match:
            return match.group(1)
    return None

def format_subscriber_count(sub_count_str):
    """Formats subscriber count into a human-readable string."""
    if not sub_count_str:
        return "Unknown"

    # Remove common suffixes and clean the string
    clean_str = sub_count_str.lower().replace('subscribers', '').replace('subscriber', '').strip()

    try:
        # Handle different formats like "1.2M", "500K", "1,234"
        if 'k' in clean_str:
            num = float(clean_str.replace('k', ''))
            return f"{num:.1f}K subscribers" if num != int(num) else f"{int(num)}K subscribers"
        elif 'm' in clean_str:
            num = float(clean_str.replace('m', ''))
            return f"{num:.1f}M subscribers" if num != int(num) else f"{int(num)}M subscribers"
        elif 'b' in clean_str:
            num = float(clean_str.replace('b', ''))
            return f"{num:.1f}B subscribers" if num != int(num) else f"{int(num)}B subscribers"
        else:
            # Handle comma-separated numbers
            num = int(clean_str.replace(',', ''))
            if num >= 1_000_000_000:
                return f"{num / 1_000_000_000:.1f}B subscribers"
            elif num >= 1_000_000:
                return f"{num / 1_000_000:.1f}M subscribers"
            elif num >= 1_000:
                return f"{num / 1_000:.1f}K subscribers"
            else:
                return f"{num:,} subscribers"
    except (ValueError, AttributeError):
        return sub_count_str

def scrape_channel_data(channel_name):
    """
    Scrapes channel data from YouTube channel about page.
    Returns a complete channel object for caching.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    # Try different URL formats to find the channel
    possible_urls = [
        f"https://www.youtube.com/@{channel_name}/about",
        f"https://www.youtube.com/c/{channel_name}/about",
        f"https://www.youtube.com/user/{channel_name}/about",
        f"https://www.youtube.com/channel/{channel_name}/about"
    ]

    for url in possible_urls:
        try:
            print(f"Trying to scrape channel data from: {url}")
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # Parse the response
            soup = BeautifulSoup(response.text, 'html.parser')

            # Initialize default values
            subscriber_count = "Unknown"
            subscriber_count_raw = 0
            channel_logo_url = ""
            channel_id = ""

            # Method 1: Try to find ytInitialData for most reliable data
            initial_data_match = re.search(r'var ytInitialData = ({.*?});', response.text)
            if initial_data_match:
                try:
                    initial_data = json.loads(initial_data_match.group(1))

                    # Navigate to find channel metadata
                    header = initial_data.get('header', {})
                    if 'c4TabbedHeaderRenderer' in header:
                        renderer = header['c4TabbedHeaderRenderer']

                        # Get subscriber count
                        if 'subscriberCountText' in renderer:
                            sub_text = renderer['subscriberCountText'].get('simpleText', '')
                            if sub_text:
                                subscriber_count = format_subscriber_count(sub_text)
                                # Extract raw number for sorting
                                try:
                                    if 'k' in sub_text.lower():
                                        subscriber_count_raw = int(float(sub_text.lower().replace('k', '').replace(' subscribers', '').strip()) * 1000)
                                    elif 'm' in sub_text.lower():
                                        subscriber_count_raw = int(float(sub_text.lower().replace('m', '').replace(' subscribers', '').strip()) * 1000000)
                                    elif 'b' in sub_text.lower():
                                        subscriber_count_raw = int(float(sub_text.lower().replace('b', '').replace(' subscribers', '').strip()) * 1000000000)
                                    else:
                                        subscriber_count_raw = int(sub_text.replace(',', '').replace(' subscribers', '').replace(' subscriber', '').strip())
                                except (ValueError, AttributeError):
                                    subscriber_count_raw = 0

                        # Get channel logo
                        if 'avatar' in renderer:
                            thumbnails = renderer['avatar'].get('thumbnails', [])
                            if thumbnails:
                                channel_logo_url = thumbnails[-1].get('url', '')

                    # Try to get channel ID from the data
                    metadata = initial_data.get('metadata', {})
                    if 'channelMetadataRenderer' in metadata:
                        channel_id = metadata['channelMetadataRenderer'].get('externalId', '')

                except (json.JSONDecodeError, KeyError, IndexError) as e:
                    print(f"Failed to parse ytInitialData for channel: {e}")

            # Method 2: Fallback - search for patterns in HTML
            if subscriber_count == "Unknown":
                # Look for subscriber count patterns
                sub_patterns = [
                    r'"subscriberCountText":{"simpleText":"([^"]+)"',
                    r'(\d+(?:\.\d+)?[KMB]?) subscribers?',
                    r'(\d{1,3}(?:,\d{3})*) subscribers?'
                ]

                for pattern in sub_patterns:
                    matches = re.findall(pattern, response.text, re.IGNORECASE)
                    if matches:
                        subscriber_count = format_subscriber_count(matches[0])
                        break

            # If we found valid data, create the channel object
            if subscriber_count != "Unknown" or channel_logo_url:
                now = datetime.now(timezone.utc)
                return {
                    'channelId': channel_id or channel_name,
                    'name': channel_name,
                    'logoUrl': channel_logo_url or f'https://www.youtube.com/img/desktop/yt_1200.png',
                    'subscriberCount': subscriber_count,
                    'subscriberCountRaw': subscriber_count_raw,
                    'lastUpdated': now.isoformat(),
                    'url': url.replace('/about', '')
                }

        except Exception as e:
            print(f"Error scraping channel from {url}: {e}")
            continue

    # If all URLs failed, return a minimal object
    print(f"Failed to scrape channel data for: {channel_name}")
    now = datetime.now(timezone.utc)
    return {
        'channelId': channel_name,
        'name': channel_name,
        'logoUrl': 'https://www.youtube.com/img/desktop/yt_1200.png',
        'subscriberCount': 'Unknown',
        'subscriberCountRaw': 0,
        'lastUpdated': now.isoformat(),
        'url': f'https://www.youtube.com/@{channel_name}'
    }

# --- FLASK ROUTES ---

@app.route('/')
def index():
    """Main page - BLAZING FAST! No network requests, pure cache reading with groups support."""
    # Load complete data structure (NO SCRAPING!)
    data = load_data()
    videos = data['videos']
    groups = data['groups']

    # PHASE 1: PRE-CALCULATE VIDEO COUNTS FOR EACH GROUP
    # This eliminates the need for complex template logic and improves performance
    enriched_groups = []
    for group in groups:
        # Create a copy of the group to avoid modifying the original
        enriched_group = group.copy()
        # Calculate how many videos belong to this group
        video_count = sum(1 for video in videos if group['id'] in video.get('groups', []))
        # Add the pre-calculated count to the group object
        enriched_group['video_count'] = video_count
        enriched_groups.append(enriched_group)

    # Get filtering and sorting preferences from URL parameters
    group_filter = request.args.get('group_filter', default='all')
    sort_by = request.args.get('sort_by', default='date_added_newest')

    # Apply group filtering (INSTANT - all data is local!)
    filtered_videos = filter_videos_by_group(videos, group_filter)

    # Prepare videos for display (updates timeAgo and adds group names)
    filtered_videos = prepare_videos_for_display(filtered_videos, groups)

    # Apply sorting logic (INSTANT - all data is local!)
    if sort_by == 'date_added_oldest':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('dateAdded', ''))
    elif sort_by == 'popular':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('viewCountRaw', 0), reverse=True)
    elif sort_by == 'date_published_newest':
        # Sort by publish date, handling None values
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('publishDateRaw') or '', reverse=True)
    elif sort_by == 'date_published_oldest':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('publishDateRaw') or 'z')
    elif sort_by == 'alphabetical':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('title', '').lower())
    elif sort_by == 'channel':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('channelName', '').lower())
    else:  # 'date_added_newest' - default
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('dateAdded', ''), reverse=True)

    return render_template('index.html',
                         videos=filtered_videos,
                         groups=enriched_groups,  # Pass enriched groups with video_count
                         sort_by=sort_by,
                         group_filter=group_filter)

@app.route('/add_video', methods=['POST'])
def add_video():
    """THE ONLY ROUTE THAT SCRAPES! Adds new videos to cache."""
    video_urls = request.form.get('video_url', '').strip()
    if not video_urls:
        return redirect(url_for('index'))

    # Load existing cache
    videos = load_video_cache()

    # Split by lines to support bulk adding
    url_lines = video_urls.splitlines()
    new_videos_added = 0

    for line in url_lines:
        line = line.strip()
        if not line:
            continue

        video_id = get_video_id(line)
        if not video_id:
            continue

        # Check if video already exists in cache
        if find_video_by_id(videos, video_id):
            print(f"Video {video_id} already exists in cache, skipping...")
            continue

        # SCRAPE THE NEW VIDEO (this is the only place we do this!)
        print(f"Scraping new video: {video_id}")
        video_object = scrape_youtube_video_to_object(video_id)

        if video_object:
            # Add to the beginning of the list (newest first)
            videos.insert(0, video_object)
            new_videos_added += 1
            print(f"Successfully added: {video_object.get('title', video_id)}")

    # Save updated cache
    if new_videos_added > 0:
        save_video_cache(videos)
        print(f"Added {new_videos_added} new video(s) to cache!")

    return redirect(url_for('index'))

@app.route('/remove_video/<video_id>')
def remove_video(video_id):
    """INSTANT removal from cache - no network requests!"""
    videos = load_video_cache()

    # Find and remove the video object
    videos = [video for video in videos if video.get('id') != video_id]

    # Save updated cache
    save_video_cache(videos)
    print(f"Removed video {video_id} from cache")

    return redirect(url_for('index'))

@app.route('/refresh_all')
def refresh_all():
    """User-triggered refresh of all video data. This is intentionally slow."""
    videos = load_video_cache()

    if not videos:
        return redirect(url_for('index'))

    print(f"Refreshing data for {len(videos)} videos...")
    updated_videos = []

    for i, video in enumerate(videos):
        video_id = video.get('id')
        if not video_id:
            updated_videos.append(video)
            continue

        print(f"Refreshing {i+1}/{len(videos)}: {video_id}")

        # Re-scrape the video
        fresh_data = scrape_youtube_video_to_object(video_id)

        if fresh_data:
            # Preserve the original dateAdded
            fresh_data['dateAdded'] = video.get('dateAdded', fresh_data['dateAdded'])
            updated_videos.append(fresh_data)
        else:
            # Keep old data if scraping fails
            updated_videos.append(video)

        # Rate limiting
        if i < len(videos) - 1:
            time.sleep(1)

    # Save refreshed cache
    save_video_cache(updated_videos)
    print("Refresh complete!")

    return redirect(url_for('index'))

@app.route('/export/<format>')
def export_data(format):
    """Export video data in CSV or JSON format."""
    data = load_data()
    videos = data['videos']

    if format == 'json':
        # JSON Export - return the complete data structure
        response = Response(
            json.dumps(data, indent=2),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment; filename=youtube_videos_export.json'}
        )
        return response

    elif format == 'csv':
        # CSV Export - create CSV from video data
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header row
        writer.writerow([
            'ID', 'Title', 'URL', 'Channel Name', 'Views (Formatted)', 'Views (Raw)',
            'Upload Date', 'Upload Date (Raw)', 'Length', 'Groups', 'Date Added', 'Last Updated'
        ])

        # Write video data rows
        for video in videos:
            writer.writerow([
                video.get('id', ''),
                video.get('title', ''),
                video.get('url', ''),
                video.get('channelName', ''),
                video.get('views', ''),
                video.get('viewCountRaw', 0),
                video.get('uploadDate', ''),
                video.get('publishDateRaw', ''),
                video.get('length', ''),
                ', '.join(video.get('groups', [])),
                video.get('dateAdded', ''),
                video.get('lastUpdated', '')
            ])

        # Create response
        response = Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment; filename=youtube_videos_export.csv'}
        )
        return response

    else:
        return redirect(url_for('index'))

@app.route('/create_group', methods=['POST'])
def create_group_route():
    """Create a new group."""
    group_name = request.form.get('group_name', '').strip()
    if not group_name:
        return redirect(url_for('index'))

    data = load_data()
    new_group = create_group(group_name)
    data['groups'].append(new_group)
    save_data(data)

    print(f"Created new group: {group_name}")
    return redirect(url_for('index'))

@app.route('/delete_group/<group_id>')
def delete_group_route(group_id):
    """Delete a group and remove it from all videos."""
    data = load_data()

    # Remove the group from the groups list
    data['groups'] = [group for group in data['groups'] if group.get('id') != group_id]

    # Remove the group from all videos
    for video in data['videos']:
        if group_id in video.get('groups', []):
            video['groups'].remove(group_id)

    save_data(data)
    print(f"Deleted group: {group_id}")
    return redirect(url_for('index'))

@app.route('/update_video_groups', methods=['POST'])
def update_video_groups():
    """Update the groups for a specific video."""
    video_id = request.form.get('video_id')
    selected_groups = request.form.getlist('groups')  # List of selected group IDs

    if not video_id:
        return redirect(url_for('index'))

    data = load_data()
    video = find_video_by_id(data['videos'], video_id)

    if video:
        video['groups'] = selected_groups
        save_data(data)
        print(f"Updated groups for video {video_id}: {selected_groups}")

    return redirect(url_for('index'))

@app.route('/channels')
def channels():
    """Channels dashboard - displays all unique channels with subscriber counts and sorting."""
    # Load video data to get unique channels
    data = load_data()
    videos = data['videos']

    # Get unique channel names from videos
    unique_channel_names = get_unique_channels_from_videos(videos)

    if not unique_channel_names:
        # No channels to display
        return render_template('channels.html', channels=[], sort_by='name')

    # Load existing channel cache
    channel_cache = load_channel_data()

    # Check and update channel data as needed
    channels_to_scrape = []
    for channel_name in unique_channel_names:
        channel = find_channel_by_name(channel_cache, channel_name)
        if not channel or is_channel_data_stale(channel):
            channels_to_scrape.append(channel_name)

    # Scrape missing or stale channel data
    if channels_to_scrape:
        print(f"Scraping data for {len(channels_to_scrape)} channels...")
        for channel_name in channels_to_scrape:
            print(f"Scraping channel: {channel_name}")
            channel_data = scrape_channel_data(channel_name)
            if channel_data:
                # Use channel name as key for easy lookup
                channel_cache[channel_name] = channel_data
                time.sleep(1)  # Rate limiting

        # Save updated cache
        save_channel_data(channel_cache)
        print("Channel data scraping complete!")

    # Prepare channels list for display
    channels_list = []
    for channel_name in unique_channel_names:
        channel = find_channel_by_name(channel_cache, channel_name)
        if channel:
            # Count videos for this channel
            video_count = sum(1 for video in videos if video.get('channelName') == channel_name)
            channel_display = channel.copy()
            channel_display['videoCount'] = video_count
            channels_list.append(channel_display)

    # Get sorting preference
    sort_by = request.args.get('sort_by', default='name')

    # Apply sorting
    if sort_by == 'subscribers':
        channels_list = sorted(channels_list, key=lambda x: x.get('subscriberCountRaw', 0), reverse=True)
    elif sort_by == 'videos':
        channels_list = sorted(channels_list, key=lambda x: x.get('videoCount', 0), reverse=True)
    else:  # 'name' - default
        channels_list = sorted(channels_list, key=lambda x: x.get('name', '').lower())

    return render_template('channels.html', channels=channels_list, sort_by=sort_by)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

<!-- 
    Reusable Pagination Controls Template
    
    This template provides professional pagination controls that preserve all current filters
    while allowing navigation between pages. It's designed to work with both videos and channels.
    
    Required variables:
    - pagination: dict with current_page, total_pages, has_prev, has_next, etc.
    - endpoint: string - the route name ('index' or 'channels')
    - Additional filter variables to preserve (sort_by, group_filter, etc.)
-->

{% if pagination.total_pages > 1 %}
<div class="pagination-container">
    <div class="pagination-info">
        <span class="pagination-stats">
            Showing {{ pagination.start_index }}-{{ pagination.end_index }} 
            of {{ pagination.total_videos if pagination.total_videos is defined else pagination.total_channels }} 
            {{ 'videos' if pagination.total_videos is defined else 'channels' }}
        </span>
    </div>
    
    <nav class="pagination-nav" aria-label="Pagination Navigation">
        <ul class="pagination-list">
            <!-- Previous Page Button -->
            {% if pagination.has_prev %}
                <li class="pagination-item">
                    <a href="{{ url_for(endpoint, 
                                      page=pagination.prev_page,
                                      sort_by=sort_by,
                                      group_filter=group_filter if group_filter is defined else none,
                                      channel_id=channel_filter if channel_filter is defined else none,
                                      show_grouped=show_grouped_only if show_grouped_only else none,
                                      q=search_query if search_query is defined else none,
                                      min_views=min_views if min_views is defined else none,
                                      max_views=max_views if max_views is defined else none) }}" 
                       class="pagination-link pagination-prev"
                       title="Previous page">
                        <span class="material-symbols-outlined">chevron_left</span>
                        <span class="pagination-text">Previous</span>
                    </a>
                </li>
            {% else %}
                <li class="pagination-item pagination-disabled">
                    <span class="pagination-link pagination-prev">
                        <span class="material-symbols-outlined">chevron_left</span>
                        <span class="pagination-text">Previous</span>
                    </span>
                </li>
            {% endif %}
            
            <!-- Page Numbers -->
            {% set start_page = [1, pagination.current_page - 2] | max %}
            {% set end_page = [pagination.total_pages, pagination.current_page + 2] | min %}
            
            <!-- First page + ellipsis if needed -->
            {% if start_page > 1 %}
                <li class="pagination-item">
                    <a href="{{ url_for(endpoint, 
                                      page=1,
                                      sort_by=sort_by,
                                      group_filter=group_filter if group_filter is defined else none,
                                      channel_id=channel_filter if channel_filter is defined else none,
                                      show_grouped=show_grouped_only if show_grouped_only else none,
                                      q=search_query if search_query is defined else none,
                                      min_views=min_views if min_views is defined else none,
                                      max_views=max_views if max_views is defined else none) }}" 
                       class="pagination-link">1</a>
                </li>
                {% if start_page > 2 %}
                    <li class="pagination-item pagination-ellipsis">
                        <span class="pagination-link">...</span>
                    </li>
                {% endif %}
            {% endif %}
            
            <!-- Current page range -->
            {% for page_num in range(start_page, end_page + 1) %}
                <li class="pagination-item">
                    {% if page_num == pagination.current_page %}
                        <span class="pagination-link pagination-current" aria-current="page">{{ page_num }}</span>
                    {% else %}
                        <a href="{{ url_for(endpoint, 
                                          page=page_num,
                                          sort_by=sort_by,
                                          group_filter=group_filter if group_filter is defined else none,
                                          channel_id=channel_filter if channel_filter is defined else none,
                                          show_grouped=show_grouped_only if show_grouped_only else none,
                                          q=search_query if search_query is defined else none,
                                          min_views=min_views if min_views is defined else none,
                                          max_views=max_views if max_views is defined else none) }}" 
                           class="pagination-link">{{ page_num }}</a>
                    {% endif %}
                </li>
            {% endfor %}
            
            <!-- Last page + ellipsis if needed -->
            {% if end_page < pagination.total_pages %}
                {% if end_page < pagination.total_pages - 1 %}
                    <li class="pagination-item pagination-ellipsis">
                        <span class="pagination-link">...</span>
                    </li>
                {% endif %}
                <li class="pagination-item">
                    <a href="{{ url_for(endpoint, 
                                      page=pagination.total_pages,
                                      sort_by=sort_by,
                                      group_filter=group_filter if group_filter is defined else none,
                                      channel_id=channel_filter if channel_filter is defined else none,
                                      show_grouped=show_grouped_only if show_grouped_only else none,
                                      q=search_query if search_query is defined else none,
                                      min_views=min_views if min_views is defined else none,
                                      max_views=max_views if max_views is defined else none) }}" 
                       class="pagination-link">{{ pagination.total_pages }}</a>
                </li>
            {% endif %}
            
            <!-- Next Page Button -->
            {% if pagination.has_next %}
                <li class="pagination-item">
                    <a href="{{ url_for(endpoint, 
                                      page=pagination.next_page,
                                      sort_by=sort_by,
                                      group_filter=group_filter if group_filter is defined else none,
                                      channel_id=channel_filter if channel_filter is defined else none,
                                      show_grouped=show_grouped_only if show_grouped_only else none,
                                      q=search_query if search_query is defined else none,
                                      min_views=min_views if min_views is defined else none,
                                      max_views=max_views if max_views is defined else none) }}" 
                       class="pagination-link pagination-next"
                       title="Next page">
                        <span class="pagination-text">Next</span>
                        <span class="material-symbols-outlined">chevron_right</span>
                    </a>
                </li>
            {% else %}
                <li class="pagination-item pagination-disabled">
                    <span class="pagination-link pagination-next">
                        <span class="pagination-text">Next</span>
                        <span class="material-symbols-outlined">chevron_right</span>
                    </span>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

"""
YouTube Competitor Analysis Tool - Main Application

A Flask web application for tracking and analyzing YouTube videos and channels.
This is the main controller that handles HTTP routes and coordinates between modules.
"""

import time
import csv
import io
import json
import threading
from datetime import datetime, timezone
from flask import Flask, render_template, request, redirect, url_for, Response, session

# Import our custom modules
from src.data_manager import (
    load_data, save_data, load_video_cache, save_video_cache,
    find_video_by_id, find_group_by_id, create_group, filter_videos_by_group, filter_videos_by_channel,
    load_channel_data, save_channel_data, find_channel_by_name,
    is_channel_data_stale, get_unique_channels_from_videos,
    get_canonical_channel_identifiers_from_videos, purge_stale_channels_from_cache
)
from src.scraper import get_video_id, scrape_youtube_video_to_object, scrape_channel_data, extract_channel_id_from_url, background_scrape_and_save
from src.utils import prepare_videos_for_display, resource_path

# Initialize the Flask app with PyInstaller-compatible paths
app = Flask(
    __name__,
    template_folder=resource_path('templates'),
    static_folder=resource_path('static')
)

# Configure Flask sessions for state persistence
app.secret_key = 'youtube-competitor-tool-secret-key-change-in-production'


# --- FLASK ROUTES ---

@app.route('/')
def index():
    """Main page - BLAZING FAST! No network requests, pure cache reading with groups support."""
    # Load complete data structure (NO SCRAPING!)
    data = load_data()
    videos = data['videos']
    groups = data['groups']

    # PHASE 1: PRE-CALCULATE VIDEO COUNTS FOR EACH GROUP
    # This eliminates the need for complex template logic and improves performance
    enriched_groups = []
    for group in groups:
        # Create a copy of the group to avoid modifying the original
        enriched_group = group.copy()
        # Calculate how many videos belong to this group
        video_count = sum(1 for video in videos if group['id'] in video.get('groups', []))
        # Add the pre-calculated count to the group object
        enriched_group['video_count'] = video_count
        enriched_groups.append(enriched_group)

    # Get filtering and sorting preferences with session persistence
    # If parameters are provided in URL, store them in session and use them
    if request.args.get('sort_by'):
        session['video_sort_by'] = request.args.get('sort_by')
    if request.args.get('group_filter'):
        session['video_group_filter'] = request.args.get('group_filter')

    # Use session values as defaults, with fallbacks
    group_filter = request.args.get('group_filter') or session.get('video_group_filter', 'all')
    channel_filter = request.args.get('channel_id', 'all')  # Channel filtering (no session persistence for now)
    sort_by = request.args.get('sort_by') or session.get('video_sort_by', 'date_added_newest')
    show_grouped_only = request.args.get('show_grouped') == 'true'  # NEW: Grouped videos filter

    # ADVANCED SEARCH AND FILTERING PARAMETERS
    search_query = request.args.get('q', '').strip()  # Search term
    min_views = request.args.get('min_views', type=int)  # Minimum view count
    max_views = request.args.get('max_views', type=int)  # Maximum view count
    date_from = request.args.get('date_from', '').strip()  # Upload date from (YYYY-MM-DD)
    date_to = request.args.get('date_to', '').strip()  # Upload date to (YYYY-MM-DD)

    # Apply filtering (INSTANT - all data is local!)
    filtered_videos = videos

    # Apply group filtering first
    if group_filter and group_filter != 'all':
        filtered_videos = filter_videos_by_group(filtered_videos, group_filter)

    # Apply channel filtering second (can be combined with group filtering)
    if channel_filter and channel_filter != 'all':
        filtered_videos = filter_videos_by_channel(filtered_videos, channel_filter)

    # ADVANCED SEARCH FILTERING - Search across title, channel, and description
    if search_query:
        search_lower = search_query.lower()
        filtered_videos = [
            v for v in filtered_videos
            if (search_lower in v.get('title', '').lower() or
                search_lower in v.get('channelName', '').lower() or
                search_lower in v.get('description', '').lower())
        ]
        print(f"🔍 Search '{search_query}' found {len(filtered_videos)} videos")

    # ADVANCED NUMERIC FILTERING - View count ranges
    if min_views is not None:
        filtered_videos = [v for v in filtered_videos if v.get('viewCountRaw', 0) >= min_views]
        print(f"📊 Filtered to {len(filtered_videos)} videos with >= {min_views:,} views")

    if max_views is not None:
        filtered_videos = [v for v in filtered_videos if v.get('viewCountRaw', 0) <= max_views]
        print(f"📊 Filtered to {len(filtered_videos)} videos with <= {max_views:,} views")

    # ADVANCED DATE FILTERING - Upload date ranges
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            filtered_videos = [
                v for v in filtered_videos
                if v.get('publishDateRaw') and
                datetime.fromisoformat(v['publishDateRaw'].replace('Z', '+00:00')).date() >= date_from_obj
            ]
            print(f"📅 Filtered to {len(filtered_videos)} videos from {date_from} onwards")
        except ValueError:
            print(f"⚠ Invalid date_from format: {date_from}")

    if date_to:
        try:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            filtered_videos = [
                v for v in filtered_videos
                if v.get('publishDateRaw') and
                datetime.fromisoformat(v['publishDateRaw'].replace('Z', '+00:00')).date() <= date_to_obj
            ]
            print(f"📅 Filtered to {len(filtered_videos)} videos until {date_to}")
        except ValueError:
            print(f"⚠ Invalid date_to format: {date_to}")

    # Apply "grouped videos only" filter (show only videos with groups assigned)
    if show_grouped_only:
        filtered_videos = [v for v in filtered_videos if v.get('groups')]
        print(f"📋 Filtered to {len(filtered_videos)} grouped videos only")

    # Prepare videos for display (updates timeAgo and adds group names)
    filtered_videos = prepare_videos_for_display(filtered_videos, groups)

    # Apply sorting logic (INSTANT - all data is local!)
    if sort_by == 'date_added_oldest':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('dateAdded', ''))
    elif sort_by == 'popular':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('viewCountRaw', 0), reverse=True)
    elif sort_by == 'date_published_newest':
        # Sort by publish date, handling None values
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('publishDateRaw') or '', reverse=True)
    elif sort_by == 'date_published_oldest':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('publishDateRaw') or 'z')
    elif sort_by == 'alphabetical':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('title', '').lower())
    elif sort_by == 'channel':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('channelName', '').lower())
    else:  # 'date_added_newest' - default
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('dateAdded', ''), reverse=True)

    # PAGINATION LOGIC - Slice sorted data for current page
    page = request.args.get('page', 1, type=int)
    per_page = 24  # Number of videos per page (4x6 grid)

    # Calculate pagination metadata
    total_videos = len(filtered_videos)
    total_pages = (total_videos + per_page - 1) // per_page  # Ceiling division

    # Ensure page is within valid range
    if page < 1:
        page = 1
    elif page > total_pages and total_pages > 0:
        page = total_pages

    # Calculate slice indices
    start_index = (page - 1) * per_page
    end_index = start_index + per_page

    # Slice the sorted data for current page
    paginated_videos = filtered_videos[start_index:end_index]

    # Pagination metadata for template
    pagination = {
        'current_page': page,
        'total_pages': total_pages,
        'total_videos': total_videos,
        'per_page': per_page,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_page': page - 1 if page > 1 else None,
        'next_page': page + 1 if page < total_pages else None,
        'start_index': start_index + 1,  # 1-based for display
        'end_index': min(end_index, total_videos)  # Don't exceed total
    }

    # Check if there are any pending videos (for auto-refresh)
    has_pending_videos = any(video.get('status') == 'pending' for video in filtered_videos)

    return render_template('index.html',
                         videos=paginated_videos,  # PAGINATED data instead of all
                         groups=enriched_groups,  # Pass enriched groups with video_count
                         sort_by=sort_by,
                         group_filter=group_filter,
                         channel_filter=channel_filter,
                         show_grouped_only=show_grouped_only,  # NEW: Pass grouped filter state
                         pagination=pagination,  # NEW: Pagination metadata
                         search_query=search_query,  # NEW: Search parameters
                         min_views=min_views,
                         max_views=max_views,
                         date_from=date_from,
                         date_to=date_to,
                         has_pending_videos=has_pending_videos)


@app.route('/add_video', methods=['POST'])
def add_video():
    """
    REVOLUTIONARY ASYNCHRONOUS VIDEO ADDITION!

    This route now provides INSTANT user feedback by:
    1. Creating placeholder objects immediately
    2. Launching background threads for scraping
    3. Redirecting user instantly (no waiting!)

    The user sees their videos appear immediately as "Scraping..."
    and they get updated with real data as background workers complete.
    """
    video_urls = request.form.get('video_url', '').strip()

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or session.get('video_sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or session.get('video_group_filter', 'all')

    if not video_urls:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    # Load existing cache
    videos = load_video_cache()

    # Split by lines to support bulk adding
    url_lines = video_urls.splitlines()
    new_placeholders_added = 0
    background_threads = []
    all_video_ids = []  # Collect all video IDs (from individual videos and playlists)

    print(f"🚀 Starting asynchronous video addition for {len(url_lines)} URLs...")

    for line in url_lines:
        line = line.strip()
        if not line:
            continue

        # PLAYLIST DETECTION - Check if this is a playlist URL
        if 'list=' in line and ('playlist' in line or 'watch' in line):
            print(f"🎵 Detected playlist URL: {line}")

            # Import the playlist scraper
            from src.scraper import scrape_playlist_for_video_ids

            # Extract video IDs from playlist
            playlist_video_ids = scrape_playlist_for_video_ids(line)

            if playlist_video_ids:
                print(f"🎵 Playlist contains {len(playlist_video_ids)} videos")
                all_video_ids.extend(playlist_video_ids)
            else:
                print(f"⚠️ No videos found in playlist: {line}")
            continue

        # INDIVIDUAL VIDEO PROCESSING
        video_id = get_video_id(line)
        if not video_id:
            print(f"⚠️ Could not extract video ID from: {line}")
            continue

        all_video_ids.append(video_id)

    print(f"📋 Total video IDs to process: {len(all_video_ids)} (from {len(url_lines)} input URLs)")

    # Process all collected video IDs
    for video_id in all_video_ids:

        # Check if video already exists in cache
        if find_video_by_id(videos, video_id):
            print(f"📋 Video {video_id} already exists in cache, skipping...")
            continue

        # CREATE PLACEHOLDER OBJECT (INSTANT!)
        now = datetime.now(timezone.utc)
        placeholder = {
            'id': video_id,
            'title': 'Scraping...',
            'url': f'https://www.youtube.com/watch?v={video_id}',
            'channelName': 'Loading...',
            'channelId': '',
            'channelIconUrl': 'https://www.youtube.com/img/desktop/yt_1200.png',
            'views': 'Loading...',
            'viewCountRaw': 0,
            'uploadDate': 'Loading...',
            'publishDateRaw': '',
            'timeAgo': '',
            'length': '0:00',
            'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
            'groups': [],
            'dateAdded': now.isoformat(),
            'lastUpdated': now.isoformat(),
            'status': 'pending'  # KEY: This marks it as being processed
        }

        # Add placeholder to the beginning of the list (newest first)
        videos.insert(0, placeholder)
        new_placeholders_added += 1
        print(f"📝 Created placeholder for: {video_id}")

        # LAUNCH BACKGROUND THREAD (NON-BLOCKING!)
        thread = threading.Thread(
            target=background_scrape_and_save,
            args=(video_id,),
            daemon=True  # Thread dies when main program exits
        )
        thread.start()
        background_threads.append(thread)
        print(f"🔄 Launched background worker for: {video_id}")

    # SAVE PLACEHOLDERS IMMEDIATELY (INSTANT UI UPDATE!)
    if new_placeholders_added > 0:
        save_video_cache(videos)
        print(f"✅ Added {new_placeholders_added} placeholder(s) to cache!")
        print(f"🔄 {len(background_threads)} background workers are now scraping...")

    # INSTANT REDIRECT - User sees placeholders immediately!
    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/remove_video/<video_id>')
def remove_video(video_id):
    """INSTANT removal from cache - no network requests!"""
    videos = load_video_cache()

    # Find and remove the video object
    videos = [video for video in videos if video.get('id') != video_id]

    # Save updated cache
    save_video_cache(videos)
    print(f"Removed video {video_id} from cache")

    # Preserve sorting state
    current_sort_by = request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.args.get('group_filter', 'all')

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/refresh_all')
def refresh_all():
    """User-triggered refresh of all video data. This is intentionally slow."""
    videos = load_video_cache()

    # Preserve sorting state
    current_sort_by = request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.args.get('group_filter', 'all')

    if not videos:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    print(f"Refreshing data for {len(videos)} videos...")
    updated_videos = []

    for i, video in enumerate(videos):
        video_id = video.get('id')
        if not video_id:
            updated_videos.append(video)
            continue

        print(f"Refreshing {i+1}/{len(videos)}: {video_id}")

        # Re-scrape the video
        fresh_data = scrape_youtube_video_to_object(video_id)

        if fresh_data:
            # SMART UPDATE ALGORITHM - Preserve ALL local metadata
            old_video_object = video

            # Essential Preservation Step - NEVER lose user's work!
            if 'groups' in old_video_object:
                fresh_data['groups'] = old_video_object['groups']
            if 'dateAdded' in old_video_object:
                fresh_data['dateAdded'] = old_video_object['dateAdded']

            # Preserve any other local-only metadata that might exist
            for local_field in ['customNotes', 'userRating', 'tags']:
                if local_field in old_video_object:
                    fresh_data[local_field] = old_video_object[local_field]

            updated_videos.append(fresh_data)
            print(f"✓ Updated video {video_id} while preserving {len(old_video_object.get('groups', []))} group assignments")
        else:
            # Keep old data if scraping fails
            updated_videos.append(video)
            print(f"⚠ Failed to refresh {video_id}, keeping existing data")

        # Rate limiting
        if i < len(videos) - 1:
            time.sleep(1)

    # Save refreshed cache
    save_video_cache(updated_videos)
    print("Refresh complete!")

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/export/<format>')
def export_data(format):
    """Export video data in CSV or JSON format."""
    data = load_data()
    videos = data['videos']

    if format == 'json':
        # JSON Export - return the complete data structure
        response = Response(
            json.dumps(data, indent=2),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment; filename=youtube_videos_export.json'}
        )
        return response

    elif format == 'csv':
        # CSV Export - create CSV from video data
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header row - COMPLETE FIELD SET for round-trip compatibility
        writer.writerow([
            'ID', 'Title', 'URL', 'Channel Name', 'Channel ID', 'Channel Icon URL',
            'Views (Formatted)', 'Views (Raw)', 'Upload Date', 'Upload Date (Raw)',
            'Length', 'Thumbnail', 'Groups', 'Date Added', 'Last Updated', 'Status'
        ])

        # Write video data rows - COMPLETE DATA for round-trip compatibility
        for video in videos:
            writer.writerow([
                video.get('id', ''),
                video.get('title', ''),
                video.get('url', ''),
                video.get('channelName', ''),
                video.get('channelId', ''),
                video.get('channelIconUrl', ''),
                video.get('views', ''),
                video.get('viewCountRaw', 0),
                video.get('uploadDate', ''),
                video.get('publishDateRaw', ''),
                video.get('length', ''),
                video.get('thumbnail', ''),
                ', '.join(video.get('groups', [])),
                video.get('dateAdded', ''),
                video.get('lastUpdated', ''),
                video.get('status', 'complete')
            ])

        # Create response
        response = Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment; filename=youtube_videos_export.csv'}
        )
        return response

    else:
        # Preserve sorting state for invalid export format
        current_sort_by = request.args.get('sort_by', 'date_added_newest')
        current_group_filter = request.args.get('group_filter', 'all')
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/create_group', methods=['POST'])
def create_group_route():
    """Create a new group."""
    group_name = request.form.get('group_name', '').strip()

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or request.args.get('group_filter', 'all')

    if not group_name:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    data = load_data()
    new_group = create_group(group_name)
    data['groups'].append(new_group)
    save_data(data)

    print(f"Created new group: {group_name}")
    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/delete_group/<group_id>')
def delete_group_route(group_id):
    """Delete a group and remove it from all videos."""
    data = load_data()

    # Remove the group from the groups list
    data['groups'] = [group for group in data['groups'] if group.get('id') != group_id]

    # Remove the group from all videos
    for video in data['videos']:
        if group_id in video.get('groups', []):
            video['groups'].remove(group_id)

    save_data(data)
    print(f"Deleted group: {group_id}")

    # Preserve sorting state
    current_sort_by = request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.args.get('group_filter', 'all')

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/update_video_groups', methods=['POST'])
def update_video_groups():
    """Update the groups for a specific video."""
    video_id = request.form.get('video_id')
    selected_groups = request.form.getlist('groups')  # List of selected group IDs

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or request.args.get('group_filter', 'all')

    if not video_id:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    data = load_data()
    video = find_video_by_id(data['videos'], video_id)

    if video:
        video['groups'] = selected_groups
        save_data(data)
        print(f"Updated groups for video {video_id}: {selected_groups}")

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/channels')
def channels():
    """
    Channels dashboard with STRICT DATA INTEGRITY.

    Implements single source of truth: only channels that exist in videos are displayed.
    Automatically purges stale channel data to maintain consistency.
    """
    # Load video data to establish canonical channel list
    data = load_data()
    videos = data['videos']

    # STEP 1: Create canonical list of channel identifiers from videos (SINGLE SOURCE OF TRUTH)
    canonical_identifiers = get_canonical_channel_identifiers_from_videos(videos)

    if not canonical_identifiers:
        # No channels to display
        return render_template('channels.html', channels=[], sort_by='name')

    # STEP 2: Load and clean channel cache (PURGE STALE DATA)
    channel_cache = load_channel_data()

    # Purge channels that no longer exist in any videos
    cleaned_cache = purge_stale_channels_from_cache(channel_cache, canonical_identifiers)

    # Save cleaned cache if changes were made
    if len(cleaned_cache) != len(channel_cache):
        save_channel_data(cleaned_cache)
        print(f"Purged {len(channel_cache) - len(cleaned_cache)} stale channels from cache")

    channel_cache = cleaned_cache

    # STEP 3: Identify channels that need scraping (missing or stale) - USE CHANNEL ID AS PRIMARY KEY
    channels_to_scrape = []
    for identifier, channel_name in canonical_identifiers:
        # Try to find by channel ID first (if identifier is a channel ID), then by name
        channel = None
        if identifier.startswith('UC'):  # This is a channel ID
            # Look for channel by ID in cache
            for cached_channel in channel_cache.values():
                if cached_channel.get('channelId') == identifier:
                    channel = cached_channel
                    break

        # Fallback to name-based lookup if no ID match found
        if not channel:
            channel = find_channel_by_name(channel_cache, channel_name)

        if not channel or is_channel_data_stale(channel):
            channels_to_scrape.append(channel_name)

    # STEP 4: Scrape missing or stale channel data
    if channels_to_scrape:
        print(f"Scraping data for {len(channels_to_scrape)} channels...")
        for channel_name in channels_to_scrape:
            print(f"Scraping channel: {channel_name}")
            channel_data = scrape_channel_data(channel_name)
            if channel_data:
                # Use channel name as key for easy lookup (maintain backward compatibility)
                channel_cache[channel_name] = channel_data
                time.sleep(1)  # Rate limiting

        # Save updated cache
        save_channel_data(channel_cache)
        print("Channel data scraping complete!")

    # STEP 5: Prepare channels list for display (ONLY CANONICAL CHANNELS) - CHANNEL ID PRIORITY
    channels_list = []
    for identifier, channel_name in canonical_identifiers:
        # Find channel using the same logic as step 3
        channel = None
        if identifier.startswith('UC'):  # This is a channel ID
            # Look for channel by ID in cache
            for cached_channel in channel_cache.values():
                if cached_channel.get('channelId') == identifier:
                    channel = cached_channel
                    break

        # Fallback to name-based lookup if no ID match found
        if not channel:
            channel = find_channel_by_name(channel_cache, channel_name)

        if channel:
            # Count videos for this channel using AUTHORITATIVE CHANNEL ID MATCHING
            video_count = 0
            for video in videos:
                video_channel_id = video.get('channelId', '')
                video_channel_name = video.get('channelName', '')

                # PRIORITY 1: Match by channelId (most reliable)
                if video_channel_id and identifier.startswith('UC') and video_channel_id == identifier:
                    video_count += 1
                # PRIORITY 2: Match by channelName (fallback for videos without channelId)
                elif not video_channel_id and video_channel_name == channel_name:
                    video_count += 1
                # PRIORITY 3: Match by name when identifier is also a name (legacy support)
                elif not identifier.startswith('UC') and video_channel_name == identifier:
                    video_count += 1

            channel_display = channel.copy()
            channel_display['videoCount'] = video_count
            channels_list.append(channel_display)

    # Get sorting preference with session persistence
    if request.args.get('sort_by'):
        session['channel_sort_by'] = request.args.get('sort_by')

    sort_by = request.args.get('sort_by') or session.get('channel_sort_by', 'name')

    # Apply sorting
    if sort_by == 'subscribers':
        channels_list = sorted(channels_list, key=lambda x: x.get('subscriberCountRaw', 0), reverse=True)
    elif sort_by == 'videos':
        channels_list = sorted(channels_list, key=lambda x: x.get('videoCount', 0), reverse=True)
    else:  # 'name' - default
        channels_list = sorted(channels_list, key=lambda x: x.get('name', '').lower())

    # PAGINATION LOGIC for channels
    page = request.args.get('page', 1, type=int)
    per_page = 12  # Number of channels per page (3x4 grid)

    # Calculate pagination metadata
    total_channels = len(channels_list)
    total_pages = (total_channels + per_page - 1) // per_page  # Ceiling division

    # Ensure page is within valid range
    if page < 1:
        page = 1
    elif page > total_pages and total_pages > 0:
        page = total_pages

    # Calculate slice indices
    start_index = (page - 1) * per_page
    end_index = start_index + per_page

    # Slice the sorted data for current page
    paginated_channels = channels_list[start_index:end_index]

    # Pagination metadata for template
    pagination = {
        'current_page': page,
        'total_pages': total_pages,
        'total_channels': total_channels,
        'per_page': per_page,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_page': page - 1 if page > 1 else None,
        'next_page': page + 1 if page < total_pages else None,
        'start_index': start_index + 1,  # 1-based for display
        'end_index': min(end_index, total_channels)  # Don't exceed total
    }

    return render_template('channels.html',
                         channels=paginated_channels,  # PAGINATED data
                         sort_by=sort_by,
                         pagination=pagination)  # NEW: Pagination metadata


@app.route('/add_channel', methods=['POST'])
def add_channel():
    """Add a channel manually by URL to the tracking list."""
    channel_url = request.form.get('channel_url', '').strip()

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or session.get('channel_sort_by', 'name')

    if not channel_url:
        return redirect(url_for('channels', sort_by=current_sort_by))

    # Extract channel identifier from URL
    channel_identifier = extract_channel_id_from_url(channel_url)
    if not channel_identifier:
        print(f"Could not extract channel identifier from URL: {channel_url}")
        return redirect(url_for('channels', sort_by=current_sort_by))

    # Load existing channel cache
    channel_cache = load_channel_data()

    # Check if channel already exists (by identifier or name)
    existing_channel = None
    for cache_key, channel_data in channel_cache.items():
        if (channel_data.get('channelId') == channel_identifier or
            channel_data.get('name') == channel_identifier or
            cache_key == channel_identifier):
            existing_channel = channel_data
            break

    if existing_channel:
        print(f"Channel already exists: {existing_channel.get('name', channel_identifier)}")
        return redirect(url_for('channels', sort_by=current_sort_by))

    # Scrape the new channel
    print(f"Adding new channel: {channel_identifier}")
    channel_data = scrape_channel_data(channel_identifier)

    if channel_data:
        # Mark as manually added
        channel_data['manuallyAdded'] = True

        # Use channel name as key for easy lookup
        channel_name = channel_data.get('name', channel_identifier)
        channel_cache[channel_name] = channel_data

        # Save updated cache
        save_channel_data(channel_cache)
        print(f"Successfully added channel: {channel_name}")
    else:
        print(f"Failed to scrape channel data for: {channel_identifier}")

    return redirect(url_for('channels', sort_by=current_sort_by))


@app.route('/import_data', methods=['POST'])
def import_data():
    """Import video data from JSON or CSV files with smart merging."""
    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or request.args.get('group_filter', 'all')

    if 'import_file' not in request.files:
        print("No file uploaded")
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    file = request.files['import_file']
    if file.filename == '':
        print("No file selected")
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    # Load existing data
    existing_data = load_data()
    existing_videos = existing_data['videos']
    existing_groups = existing_data['groups']

    try:
        file_content = file.read().decode('utf-8')
        filename = file.filename.lower()

        imported_videos = []
        imported_groups = []

        if filename.endswith('.json'):
            # JSON Import Logic - ROBUST VALIDATION
            print("Processing JSON import...")
            imported_data = json.loads(file_content)

            # Handle different JSON structures with validation
            if isinstance(imported_data, dict):
                # Preferred format: {"videos": [...], "groups": [...]}
                imported_videos = imported_data.get('videos', [])
                imported_groups = imported_data.get('groups', [])

                # Validate that videos is a list
                if not isinstance(imported_videos, list):
                    print("Error: 'videos' field must be a list")
                    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

                # Validate that groups is a list
                if not isinstance(imported_groups, list):
                    print("Error: 'groups' field must be a list")
                    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

            elif isinstance(imported_data, list):
                # Legacy format: just a list of videos
                imported_videos = imported_data
                imported_groups = []
            else:
                print("Error: Invalid JSON structure. Expected object with 'videos' and 'groups' fields or array of videos.")
                return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

        elif filename.endswith('.csv'):
            # CSV Import Logic
            print("Processing CSV import...")
            import csv
            from io import StringIO

            csv_reader = csv.DictReader(StringIO(file_content))
            for row in csv_reader:
                # Convert CSV row back to video object structure - COMPLETE FIELD MAPPING
                video_obj = {
                    'id': row.get('ID', ''),
                    'title': row.get('Title', ''),
                    'url': row.get('URL', ''),
                    'channelName': row.get('Channel Name', ''),
                    'channelId': row.get('Channel ID', ''),
                    'channelIconUrl': row.get('Channel Icon URL', '') or f'https://i.ytimg.com/vi/{row.get("ID", "")}/default.jpg',
                    'views': row.get('Views (Formatted)', ''),
                    'viewCountRaw': int(row.get('Views (Raw)', 0) or 0),
                    'uploadDate': row.get('Upload Date', ''),
                    'publishDateRaw': row.get('Upload Date (Raw)', ''),
                    'length': row.get('Length', ''),
                    'thumbnail': row.get('Thumbnail', '') or f'https://i.ytimg.com/vi/{row.get("ID", "")}/hqdefault.jpg',
                    'groups': row.get('Groups', '').split(', ') if row.get('Groups') else [],
                    'dateAdded': row.get('Date Added', ''),
                    'lastUpdated': row.get('Last Updated', ''),
                    'status': row.get('Status', 'complete'),
                    'timeAgo': ''  # This will be calculated dynamically
                }
                imported_videos.append(video_obj)
        else:
            print(f"Unsupported file format: {filename}")
            return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

        # ENHANCED Smart Merge Logic for Videos with validation
        videos_added = 0
        videos_skipped = 0
        videos_invalid = 0

        # Create lookup set for O(1) existence checking
        existing_video_ids = {v.get('id') for v in existing_videos if v.get('id')}

        for imported_video in imported_videos:
            video_id = imported_video.get('id')
            video_title = imported_video.get('title', 'Unknown Title')

            # Validate required fields
            if not video_id:
                print(f"Skipping video with missing ID: {video_title}")
                videos_invalid += 1
                continue

            if not imported_video.get('url'):
                print(f"Skipping video with missing URL: {video_title}")
                videos_invalid += 1
                continue

            # Check if video already exists (O(1) lookup)
            if video_id not in existing_video_ids:
                # New video - add it directly
                existing_videos.append(imported_video)
                existing_video_ids.add(video_id)  # Update lookup set
                videos_added += 1
                print(f"✓ Added imported video: {video_title}")
            else:
                # Video exists - SMART UPDATE to preserve local metadata
                existing_video = find_video_by_id(existing_videos, video_id)
                if existing_video:
                    # Preserve critical local metadata (user's work must never be lost!)
                    if 'groups' in existing_video:
                        imported_video['groups'] = existing_video['groups']
                    if 'dateAdded' in existing_video:
                        imported_video['dateAdded'] = existing_video['dateAdded']

                    # Preserve any other local-only metadata
                    for local_field in ['customNotes', 'userRating', 'tags']:
                        if local_field in existing_video:
                            imported_video[local_field] = existing_video[local_field]

                    # Replace the existing video with the updated one
                    for i, video in enumerate(existing_videos):
                        if video.get('id') == video_id:
                            existing_videos[i] = imported_video
                            break

                    videos_skipped += 1  # Count as "updated" rather than "added"
                    print(f"✓ Updated existing video {video_id} while preserving {len(existing_video.get('groups', []))} group assignments")
                else:
                    videos_skipped += 1
                    print(f"⚠ Video {video_id} exists but couldn't be found for update")

        # ENHANCED Smart Merge Logic for Groups with validation
        groups_added = 0
        groups_skipped = 0
        groups_invalid = 0

        # Create lookup sets for O(1) existence checking
        existing_group_ids = {g.get('id') for g in existing_groups if g.get('id')}
        existing_group_names = {g.get('name') for g in existing_groups if g.get('name')}

        for imported_group in imported_groups:
            group_id = imported_group.get('id')
            group_name = imported_group.get('name')

            # Validate required fields
            if not group_id or not group_name:
                print(f"Skipping group with missing ID or name: {group_id}, {group_name}")
                groups_invalid += 1
                continue

            # Check if group already exists by ID or name (O(1) lookups)
            if group_id not in existing_group_ids and group_name not in existing_group_names:
                existing_groups.append(imported_group)
                existing_group_ids.add(group_id)  # Update lookup sets
                existing_group_names.add(group_name)
                groups_added += 1
                print(f"✓ Added imported group: {group_name}")
            else:
                groups_skipped += 1
                if group_id in existing_group_ids:
                    print(f"⚠ Group ID {group_id} already exists, skipping...")
                else:
                    print(f"⚠ Group name '{group_name}' already exists, skipping...")

        # Save merged data
        merged_data = {
            'videos': existing_videos,
            'groups': existing_groups
        }
        save_data(merged_data)

        # Comprehensive import summary
        print(f"\n=== IMPORT COMPLETE ===")
        print(f"✓ Videos added: {videos_added}")
        print(f"⚠ Videos skipped (duplicates): {videos_skipped}")
        print(f"✗ Videos invalid: {videos_invalid}")
        print(f"✓ Groups added: {groups_added}")
        print(f"⚠ Groups skipped (duplicates): {groups_skipped}")
        print(f"✗ Groups invalid: {groups_invalid}")
        print(f"📊 Total videos in library: {len(existing_videos)}")
        print(f"📊 Total groups in library: {len(existing_groups)}")

    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error importing data: {e}")

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/scraping_status')
def scraping_status():
    """API endpoint to check the status of background scraping jobs."""
    try:
        videos = load_video_cache()
        pending_count = sum(1 for video in videos if video.get('status') == 'pending')

        return {
            'pending_count': pending_count,
            'total_videos': len(videos),
            'status': 'active' if pending_count > 0 else 'idle'
        }
    except Exception as e:
        print(f"Error checking scraping status: {e}")
        return {'pending_count': 0, 'total_videos': 0, 'status': 'error'}


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

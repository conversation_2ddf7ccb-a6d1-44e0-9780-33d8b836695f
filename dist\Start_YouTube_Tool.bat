@echo off
echo Starting YouTube Competitor Analysis Tool...
echo.
echo The application will start in the background.
echo Your browser will open automatically in 3 seconds.
echo.
echo To stop the application later, check your system tray
echo (bottom-right corner) for the app icon and right-click it.
echo.

REM Start the application in the background
start "" "%~dp0app.exe"

REM Wait 3 seconds for the app to start
timeout /t 3 /nobreak >nul

REM Open the browser
start "" "http://127.0.0.1:5000"

echo Browser opened! The YouTube tool should now be running.
echo You can close this window.
pause

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Channels Dashboard - My YouTube Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <span class="material-symbols-outlined logo-icon">play_circle</span>
                <h1>Channels Dashboard</h1>
            </div>
            
            <div class="header-actions">
                <a href="/" class="nav-button">
                    <span class="material-symbols-outlined">video_library</span>
                    Videos
                </a>
                <a href="/channels" class="nav-button active">
                    <span class="material-symbols-outlined">account_circle</span>
                    Channels
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Add Channel Section -->
            <div class="add-channel-section">
                <form action="/add_channel" method="POST" class="add-channel-form">
                    <!-- Hidden input to preserve sorting state -->
                    <input type="hidden" name="sort_by" value="{{ sort_by }}">
                    <div class="input-container">
                        <input type="text"
                               name="channel_url"
                               placeholder="Paste YouTube channel URL here (e.g., https://www.youtube.com/@channelname)"
                               required
                               class="channel-input">
                        <button type="submit" class="add-button">
                            <span class="material-symbols-outlined">add</span>
                            Add Channel
                        </button>
                    </div>
                </form>
            </div>

            <!-- Controls Section -->
            <div class="controls-section">
                <div class="controls-left">
                    <h2 class="page-title">
                        <span class="material-symbols-outlined">account_circle</span>
                        Channels ({{ channels|length }})
                    </h2>
                </div>
                
                <div class="controls-right">
                    <!-- Sorting Controls -->
                    <div class="sort-controls">
                        <label for="sort-select" class="sort-label">Sort by:</label>
                        <select id="sort-select" class="sort-select" onchange="updateSort()">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name (A-Z)</option>
                            <option value="subscribers" {% if sort_by == 'subscribers' %}selected{% endif %}>Subscribers</option>
                            <option value="videos" {% if sort_by == 'videos' %}selected{% endif %}>Video Count</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Channels Content -->
            {% if channels %}
                <div class="channels-content">
                    <div class="channels-grid">
                    {% for channel in channels %}
                    <div class="channel-card">
                        <div class="channel-header">
                            <img src="{{ channel.logoUrl }}"
                                 alt="{{ channel.name }}"
                                 class="channel-logo"
                                 loading="lazy"
                                 onerror="this.src='https://www.youtube.com/img/desktop/yt_1200.png'">
                            
                            <div class="channel-info">
                                <h3 class="channel-name">
                                    <a href="{% if channel.channelId.startswith('UC') %}https://www.youtube.com/channel/{{ channel.channelId }}{% else %}https://www.youtube.com/@{{ channel.channelId }}{% endif %}" target="_blank" rel="noopener noreferrer">
                                        {{ channel.name }}
                                    </a>
                                </h3>
                                <p class="channel-subscribers">{{ channel.subscriberCount }}</p>
                            </div>
                        </div>
                        
                        <div class="channel-stats">
                            <div class="stat-item">
                                <span class="material-symbols-outlined">video_library</span>
                                <span class="stat-value">{{ channel.videoCount }} video{{ 's' if channel.videoCount != 1 else '' }}</span>
                            </div>
                            
                            <div class="stat-item">
                                <span class="material-symbols-outlined">schedule</span>
                                <span class="stat-value">Updated {{ channel.lastUpdated[:10] }}</span>
                            </div>
                        </div>
                        
                        <div class="channel-actions">
                            <a href="/?channel_id={{ channel.channelId }}&group_filter=all&sort_by=channel" class="action-button">
                                <span class="material-symbols-outlined">filter_list</span>
                                View Videos
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-content">
                        <span class="material-symbols-outlined empty-icon">account_circle</span>
                        <h2>No channels found</h2>
                        <p>Add some YouTube videos first to see channel information!</p>
                        <a href="/" class="empty-action-button">
                            <span class="material-symbols-outlined">add</span>
                            Add Videos
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </main>

    <script>
        function updateSort() {
            const sortSelect = document.getElementById('sort-select');
            const sortBy = sortSelect.value;
            const url = new URL(window.location);
            url.searchParams.set('sort_by', sortBy);
            window.location.href = url.toString();
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My YouTube Dashboard</title>

    <!-- Auto-refresh when videos are being scraped in background -->
    {% if has_pending_videos %}
    <meta http-equiv="refresh" content="3">
    {% endif %}

    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/nprogress.min.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <span class="material-symbols-outlined logo-icon">play_circle</span>
                <h1>My YouTube Feed</h1>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <a href="/" class="nav-button active">
                        <span class="material-symbols-outlined">video_library</span>
                        Videos
                    </a>
                    <a href="/channels" class="nav-button">
                        <span class="material-symbols-outlined">account_circle</span>
                        Channels
                    </a>
                </div>

                <form action="/add_video" method="POST" class="add-video-form">
                    <!-- Hidden inputs to preserve sorting state -->
                    <input type="hidden" name="sort_by" value="{{ sort_by }}">
                    <input type="hidden" name="group_filter" value="{{ group_filter }}">
                    <div class="input-container">
                        <textarea name="video_url"
                                  placeholder="Paste one or more YouTube video links here, each on a new line..."
                                  required
                                  class="video-input"
                                  rows="3"></textarea>
                        <button type="submit" class="add-button">
                            <span class="material-symbols-outlined">add</span>
                            Add Videos
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </header>

    <main class="main-content">
        <!-- Import/Export Controls - Always Visible -->
        <div class="controls-section">
            <div class="action-controls">
                <!-- Export Menu -->
                <div class="export-menu">
                    <button class="export-button" onclick="toggleExportMenu()">
                        <span class="material-symbols-outlined">download</span>
                        Export
                        <span class="material-symbols-outlined">expand_more</span>
                    </button>
                    <div class="export-dropdown" id="exportDropdown">
                        <a href="/export/csv?sort_by={{ sort_by }}&group_filter={{ group_filter }}" class="export-option">
                            <span class="material-symbols-outlined">table_view</span>
                            Download as CSV
                        </a>
                        <a href="/export/json?sort_by={{ sort_by }}&group_filter={{ group_filter }}" class="export-option">
                            <span class="material-symbols-outlined">code</span>
                            Download as JSON
                        </a>
                    </div>
                </div>

                <!-- Import Menu -->
                <div class="import-menu">
                    <button class="import-button" onclick="toggleImportMenu()">
                        <span class="material-symbols-outlined">upload</span>
                        Import
                        <span class="material-symbols-outlined">expand_more</span>
                    </button>
                    <div class="import-dropdown" id="importDropdown">
                        <form action="/import_data" method="POST" enctype="multipart/form-data" class="import-form">
                            <!-- Hidden inputs to preserve sorting state -->
                            <input type="hidden" name="sort_by" value="{{ sort_by }}">
                            <input type="hidden" name="group_filter" value="{{ group_filter }}">
                            <div class="import-option">
                                <label for="import_file" class="file-label">
                                    <span class="material-symbols-outlined">attach_file</span>
                                    Choose File (JSON/CSV)
                                </label>
                                <input type="file" id="import_file" name="import_file" accept=".json,.csv" class="file-input" onchange="updateFileName(this)">
                                <div class="file-name" id="fileName">No file selected</div>
                            </div>
                            <button type="submit" class="import-submit-button">
                                <span class="material-symbols-outlined">upload</span>
                                Import Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        {% if videos %}
            <!-- Video-Specific Controls Section -->
            <div class="controls-section">
                <!-- Sorting Controls -->
                <div class="sorting-controls">
                    <form method="GET" class="sort-form">
                        <label for="sort_by" class="sort-label">
                            <span class="material-symbols-outlined">sort</span>
                            Sort by:
                        </label>
                        <select name="sort_by" id="sort_by" class="sort-dropdown" onchange="this.form.submit()">
                            <option value="date_added_newest" {% if sort_by == 'date_added_newest' %}selected{% endif %}>Date added (newest)</option>
                            <option value="date_added_oldest" {% if sort_by == 'date_added_oldest' %}selected{% endif %}>Date added (oldest)</option>
                            <option value="popular" {% if sort_by == 'popular' %}selected{% endif %}>Most popular</option>
                            <option value="date_published_newest" {% if sort_by == 'date_published_newest' %}selected{% endif %}>Upload date (newest)</option>
                            <option value="date_published_oldest" {% if sort_by == 'date_published_oldest' %}selected{% endif %}>Upload date (oldest)</option>
                            <option value="alphabetical" {% if sort_by == 'alphabetical' %}selected{% endif %}>Title (A-Z)</option>
                            <option value="channel" {% if sort_by == 'channel' %}selected{% endif %}>Channel name</option>
                        </select>
                        <!-- Hidden inputs to preserve current state -->
                        <input type="hidden" name="group_filter" value="{{ group_filter }}">
                    </form>
                </div>

                <!-- Grouped Videos Filter Toggle -->
                <div class="filter-controls">
                    <form method="GET" class="filter-form">
                        <!-- Preserve current state -->
                        <input type="hidden" name="sort_by" value="{{ sort_by }}">
                        <input type="hidden" name="group_filter" value="{{ group_filter }}">

                        <label class="toggle-switch">
                            <input type="checkbox" name="show_grouped" value="true"
                                   {% if show_grouped_only %}checked{% endif %}
                                   onchange="this.form.submit()">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">
                                <span class="material-symbols-outlined">folder_managed</span>
                                Only Grouped Videos
                            </span>
                        </label>
                    </form>
                </div>

                <!-- Refresh Controls -->
                <div class="action-controls">
                    <a href="/refresh_all?sort_by={{ sort_by }}&group_filter={{ group_filter }}" class="refresh-button" onclick="return confirm('This will update all video data (view counts, etc.) and may take a while. Continue?')">
                        <span class="material-symbols-outlined">refresh</span>
                        Refresh All Data
                    </a>
                </div>
            </div>

            <!-- Main Content Layout -->
            <div class="main-layout">
                <!-- Group Sidebar -->
                <aside class="group-sidebar">
                    <div class="sidebar-header">
                        <h3>
                            <span class="material-symbols-outlined">folder</span>
                            Groups
                        </h3>
                    </div>

                    <!-- Create Group Form -->
                    <form action="/create_group" method="POST" class="create-group-form">
                        <!-- Hidden inputs to preserve sorting state -->
                        <input type="hidden" name="sort_by" value="{{ sort_by }}">
                        <input type="hidden" name="group_filter" value="{{ group_filter }}">
                        <div class="input-group">
                            <input type="text"
                                   name="group_name"
                                   placeholder="New group name..."
                                   required
                                   class="group-input">
                            <button type="submit" class="create-group-button">
                                <span class="material-symbols-outlined">add</span>
                            </button>
                        </div>
                    </form>

                    <!-- Group List -->
                    <div class="group-list">
                        <!-- All Videos Option -->
                        <a href="/?group_filter=all&sort_by={{ sort_by }}"
                           class="group-item {% if group_filter == 'all' %}active{% endif %}">
                            <span class="material-symbols-outlined">video_library</span>
                            <span class="group-name">All Videos</span>
                            <span class="video-count">{{ videos|length }}</span>
                        </a>

                        <!-- Individual Groups -->
                        {% for group in groups %}
                        <div class="group-item-container">
                            <a href="/?group_filter={{ group.id }}&sort_by={{ sort_by }}"
                               class="group-item {% if group_filter == group.id %}active{% endif %}">
                                <span class="material-symbols-outlined">folder</span>
                                <span class="group-name">{{ group.name }}</span>
                                <span class="video-count">{{ group.video_count }}</span>
                            </a>
                            <a href="/delete_group/{{ group.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                               class="delete-group-button"
                               onclick="return confirm('Delete group \'{{ group.name }}\'? Videos will not be deleted.')">
                                <span class="material-symbols-outlined">delete</span>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </aside>

                <!-- Video Content -->
                <div class="video-content">
                    <div class="video-grid">
                {% for video in videos %}
                    {% if video.status == 'pending' %}
                        <!-- PENDING VIDEO CARD (Being Scraped) -->
                        <div class="video-card video-card-pending" data-status="pending" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <div class="thumbnail-placeholder">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.id }}"
                                         class="thumbnail thumbnail-loading"
                                         loading="lazy">
                                    <div class="loading-overlay">
                                        <div class="spinner"></div>
                                    </div>
                                </div>
                                <!-- Loading length badge -->
                                <div class="video-length-badge loading-badge">{{ video.length }}</div>

                                <div class="video-actions">
                                    <a href="/remove_video/{{ video.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button cancel-button"
                                       onclick="return confirm('Cancel scraping this video?')"
                                       title="Cancel scraping">
                                        <span class="material-symbols-outlined">cancel</span>
                                        <span class="button-text">Cancel</span>
                                    </a>
                                </div>
                            </div>

                            <div class="video-info">
                                <div class="channel-icon-placeholder">
                                    <div class="spinner-small"></div>
                                </div>

                                <div class="video-text">
                                    <h3 class="video-title loading-title">
                                        <span class="loading-text">{{ video.title }}</span>
                                        <div class="loading-dots">
                                            <span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </h3>
                                    <p class="channel-name loading-channel">{{ video.channelName }}</p>
                                    <p class="video-meta loading-meta">{{ video.views }} • {{ video.uploadDate }}</p>
                                </div>
                            </div>
                        </div>
                    {% elif video.status == 'error' %}
                        <!-- ERROR VIDEO CARD (Failed to Scrape) -->
                        <div class="video-card video-card-error" data-status="error" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <div class="thumbnail-error">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.id }}"
                                         class="thumbnail"
                                         loading="lazy">
                                    <div class="error-overlay">
                                        <span class="material-symbols-outlined error-icon">error</span>
                                    </div>
                                </div>
                                <div class="video-length-badge error-badge">Error</div>

                                <div class="video-actions">
                                    <a href="/remove_video/{{ video.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button"
                                       onclick="return confirm('Remove this failed video from your feed?')"
                                       title="Remove video">
                                        <span class="material-symbols-outlined">close</span>
                                    </a>
                                </div>
                            </div>

                            <div class="video-info">
                                <div class="channel-icon error-icon-placeholder">
                                    <span class="material-symbols-outlined">error</span>
                                </div>

                                <div class="video-text">
                                    <h3 class="video-title error-title">{{ video.title }}</h3>
                                    <p class="channel-name error-channel">Failed to load channel</p>
                                    <p class="video-meta error-meta">{{ video.error_message or 'Scraping failed' }}</p>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- NORMAL VIDEO CARD (Completed) -->
                        <div class="video-card" data-status="complete" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <a href="{{ video.url }}" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.title }}"
                                         class="thumbnail"
                                         loading="lazy">
                                </a>
                                <!-- Video length badge -->
                                <div class="video-length-badge">{{ video.length }}</div>

                                <div class="video-actions">
                                    <button class="group-manage-button"
                                            onclick="openGroupModal('{{ video.id }}')"
                                            title="Manage groups">
                                        <span class="material-symbols-outlined">folder_open</span>
                                    </button>
                                    <a href="/remove_video/{{ video.url.split('v=')[1] }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button"
                                       onclick="return confirm('Remove this video from your feed?')"
                                       title="Remove video">
                                        <span class="material-symbols-outlined">close</span>
                                    </a>
                                </div>
                            </div>

                            <div class="video-info">
                                <img src="{{ video.channelIconUrl }}"
                                     alt="{{ video.channelName }}"
                                     class="channel-icon"
                                     loading="lazy"
                                     onerror="this.src='https://www.youtube.com/img/desktop/yt_1200.png'">

                                <div class="video-text">
                                    <h3 class="video-title">
                                        <a href="{{ video.url }}" target="_blank" rel="noopener noreferrer">
                                            {{ video.title }}
                                        </a>
                                    </h3>
                                    <p class="channel-name">
                                        <a href="{{ video.channelUrl }}" target="_blank" rel="noopener noreferrer" class="channel-link">
                                            {{ video.channelName }}
                                        </a>
                                    </p>

                                    <!-- Group Pills -->
                                    {% if video.groupDetails %}
                                    <div class="group-pills">
                                        {% for group in video.groupDetails %}
                                        <a href="/?group_filter={{ group.id }}&sort_by={{ sort_by }}" class="group-link">
                                            <span class="group-pill">{{ group.name }}</span>
                                        </a>
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <p class="video-meta">{{ video.views }} • {{ video.uploadDate }}{% if video.timeAgo %} ({{ video.timeAgo }}){% endif %}</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
                    </div>
                </div>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-content">
                    <span class="material-symbols-outlined empty-icon">video_library</span>
                    <h2>No videos yet</h2>
                    <p>Add your first YouTube video using the form above to get started!</p>
                    <div class="example-urls">
                        <p><strong>Supported URL formats:</strong></p>
                        <ul>
                            <li>https://www.youtube.com/watch?v=VIDEO_ID</li>
                            <li>https://youtu.be/VIDEO_ID</li>
                            <li>https://www.youtube.com/embed/VIDEO_ID</li>
                            <li>https://www.youtube.com/shorts/VIDEO_ID</li>
                        </ul>
                    </div>
                </div>
            </div>
        {% endif %}
    </main>

    <!-- Group Management Modal -->
    <div id="groupModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Manage Groups</h3>
                <button class="modal-close" onclick="closeGroupModal()">
                    <span class="material-symbols-outlined">close</span>
                </button>
            </div>
            <form id="groupForm" action="/update_video_groups" method="POST">
                <input type="hidden" id="modalVideoId" name="video_id" value="">
                <!-- Hidden inputs to preserve sorting state -->
                <input type="hidden" name="sort_by" value="{{ sort_by }}">
                <input type="hidden" name="group_filter" value="{{ group_filter }}">
                <div class="modal-body">
                    <div class="group-checkboxes">
                        {% for group in groups %}
                        <label class="checkbox-item">
                            <input type="checkbox" name="groups" value="{{ group.id }}" class="group-checkbox">
                            <span class="checkbox-label">{{ group.name }}</span>
                        </label>
                        {% endfor %}
                        {% if not groups %}
                        <p class="no-groups">No groups created yet. Create a group using the sidebar.</p>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-button" onclick="closeGroupModal()">Cancel</button>
                    <button type="submit" class="save-button">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 My YouTube Dashboard - Built without API keys using web scraping</p>
    </footer>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.add-video-form');
            const input = document.querySelector('.video-input');
            const button = document.querySelector('.add-button');
            
            form.addEventListener('submit', function(e) {
                const url = input.value.trim();
                if (!url) {
                    e.preventDefault();
                    alert('Please enter a YouTube URL');
                    return;
                }
                
                // Basic URL validation
                const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)/;
                if (!youtubeRegex.test(url)) {
                    e.preventDefault();
                    alert('Please enter a valid YouTube URL');
                    return;
                }
                
                // Show loading state
                button.innerHTML = '<span class="material-symbols-outlined">hourglass_empty</span> Adding...';
                button.disabled = true;
            });
            
            // Auto-focus input on page load
            input.focus();
            
            // Add keyboard shortcut (Ctrl+K or Cmd+K) to focus input
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    input.focus();
                    input.select();
                }
            });
        });

        // Video data for JavaScript access
        const videoData = {{ videos|tojson }};

        // Export menu functionality
        function toggleExportMenu() {
            const dropdown = document.getElementById('exportDropdown');
            dropdown.classList.toggle('show');
        }

        // Import menu functionality
        function toggleImportMenu() {
            const dropdown = document.getElementById('importDropdown');
            dropdown.classList.toggle('show');
        }

        function updateFileName(input) {
            const fileName = document.getElementById('fileName');
            if (input.files.length > 0) {
                fileName.textContent = input.files[0].name;
            } else {
                fileName.textContent = 'No file selected';
            }
        }

        // Close export/import menus when clicking outside
        window.onclick = function(event) {
            if (!event.target.matches('.export-button') && !event.target.closest('.export-button')) {
                const exportDropdown = document.getElementById('exportDropdown');
                if (exportDropdown.classList.contains('show')) {
                    exportDropdown.classList.remove('show');
                }
            }
            if (!event.target.matches('.import-button') && !event.target.closest('.import-button')) {
                const importDropdown = document.getElementById('importDropdown');
                if (importDropdown.classList.contains('show')) {
                    importDropdown.classList.remove('show');
                }
            }
        }

        // Group modal functionality with scroll preservation
        function openGroupModal(videoId) {
            // PRESERVE SCROLL POSITION before opening modal
            if (window.preserveScrollPosition) {
                window.preserveScrollPosition();
            }

            const modal = document.getElementById('groupModal');
            const videoIdInput = document.getElementById('modalVideoId');
            const checkboxes = document.querySelectorAll('.group-checkbox');

            // Set video ID
            videoIdInput.value = videoId;

            // Find video data and check appropriate boxes
            const video = videoData.find(v => v.id === videoId);
            if (video) {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = video.groups.includes(checkbox.value);
                });
            }

            modal.style.display = 'block';
        }

        function closeGroupModal() {
            const modal = document.getElementById('groupModal');
            modal.style.display = 'none';
        }

        // Add scroll preservation to group form submission
        const groupForm = document.getElementById('groupForm');
        if (groupForm) {
            groupForm.addEventListener('submit', function() {
                // Preserve scroll position before form submission
                if (window.preserveScrollPosition) {
                    window.preserveScrollPosition();
                }
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('groupModal');
            if (event.target === modal) {
                closeGroupModal();
            }
        }
    </script>

    <!-- NProgress - Global Progress Bar -->
    <script src="{{ url_for('static', filename='vendor/nprogress.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/progress.js') }}"></script>
    <script src="{{ url_for('static', filename='js/realtime-progress.js') }}"></script>
</body>
</html>

/**
 * Global Progress Bar Implementation using NProgress
 * 
 * Provides modern, responsive feedback during any loading operation
 * with a sleek top-of-page progress bar like YouTube or GitHub.
 */

// Configure NProgress for optimal user experience
NProgress.configure({ 
    showSpinner: false,  // Hide spinner for cleaner look
    speed: 300,          // Smooth animation speed
    minimum: 0.1,        // Start with visible progress
    trickleSpeed: 200    // Smooth trickle animation
});

// Custom CSS styling to match YouTube theme
const style = document.createElement('style');
style.textContent = `
    #nprogress .bar {
        background: #ff0000 !important; /* YouTube red */
        height: 3px !important;         /* Slightly thicker for visibility */
        box-shadow: 0 0 10px #ff0000, 0 0 5px #ff0000 !important;
    }
    
    #nprogress .peg {
        box-shadow: 0 0 10px #ff0000, 0 0 5px #ff0000 !important;
    }
`;
document.head.appendChild(style);

/**
 * Initialize progress bar functionality when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
    
    // Start progress bar on any form submission
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            NProgress.start();
        });
    });
    
    // Start progress bar on navigation links (but not hash links or external links)
    document.querySelectorAll('a[href]').forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Skip hash links, external links, and javascript: links
            if (href && 
                !href.startsWith('#') && 
                !href.startsWith('javascript:') && 
                !href.startsWith('mailto:') && 
                !href.startsWith('tel:') &&
                !this.hasAttribute('target')) {
                
                NProgress.start();
            }
        });
    });
    
    // Start progress bar when navigating away from the page
    window.addEventListener('beforeunload', function() {
        NProgress.start();
    });
    
    // Ensure progress bar completes on page load
    window.addEventListener('load', function() {
        NProgress.done();
    });
    
    // Handle browser back/forward navigation
    window.addEventListener('pageshow', function() {
        NProgress.done();
    });
    
    // Auto-complete progress bar if it's been running too long (fallback)
    setTimeout(function() {
        if (NProgress.status) {
            NProgress.done();
        }
    }, 10000); // 10 second timeout
});

/**
 * Utility functions for manual progress control
 */
window.ProgressBar = {
    start: function() {
        NProgress.start();
    },
    
    done: function() {
        NProgress.done();
    },
    
    set: function(percentage) {
        NProgress.set(percentage);
    },
    
    inc: function(amount) {
        NProgress.inc(amount);
    }
};



















# Web Scraping Preferences
- User prefers building tools without API keys when possible, using web scraping techniques to extract publicly available data like YouTube video information.
- User prefers robust web scraping by targeting machine-readable data (like meta tags with datePublished) rather than human-readable text to make tools immune to UI changes.
- User prefers implementing robust scraping with waterfall fallback systems (primary method -> fallback 1 -> fallback 2) and storing data in dual formats (raw for sorting, formatted for display) when building data-heavy applications.
- User prefers implementing 3-tier waterfall scraping strategies (JSON -> Meta Tags -> Regex patterns) for robust data extraction and adding respectful delays (time.sleep(1)) between requests to avoid rate limiting when scraping multiple pages sequentially.
- User prefers 'Scrape Once, Store Locally, Read Many Times' architecture for web scraping applications, using local JSON cache to store complete scraped data objects and only re-scraping on user-triggered refresh operations.
- User prefers separate cache files for different data types (videos.json, channels.json).

# Data Handling and UI Preferences
- User prefers implementing export functionality with multiple formats (CSV, JSON) using direct cache access and Content-Disposition headers.
- User prefers organizing features using group-based filtering with modal interfaces for assignment rather than separate pages.
- User prefers keeping templates simple by moving complex data calculations and logic to the backend (Python) rather than performing database-like queries in frontend templates (Jinja2).
- User prefers enriching data objects in backend before passing to templates rather than complex template logic.
- User prefers placeholder objects with 'pending' status for immediate UI feedback.
- User prefers meta-refresh over JavaScript polling for simpler implementation of auto-updating interfaces.

# Application Architecture
- User prefers modular application architecture with separate files for data management (data_manager.py), web scraping (scraper.py), and utilities (utils.py), keeping main app.py as a controller with only Flask routes.

# Background Processing
- User prefers threading over Celery/Redis for background processing.
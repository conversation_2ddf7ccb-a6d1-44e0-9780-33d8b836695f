('C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
 'tool\\build\\app\\app.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('app',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\app.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('channels.json',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\channels.json',
   'DATA'),
  ('src\\__init__.py',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\__init__.py',
   'DATA'),
  ('src\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\data_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\__pycache__\\data_manager.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\scraper.cpython-313.pyc',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\__pycache__\\scraper.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\utils.cpython-313.pyc',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\__pycache__\\utils.cpython-313.pyc',
   'DATA'),
  ('src\\data_manager.py',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\data_manager.py',
   'DATA'),
  ('src\\scraper.py',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\scraper.py',
   'DATA'),
  ('src\\utils.py',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\src\\utils.py',
   'DATA'),
  ('static\\style.css',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\static\\style.css',
   'DATA'),
  ('templates\\channels.html',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\templates\\channels.html',
   'DATA'),
  ('templates\\index.html',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\templates\\index.html',
   'DATA'),
  ('videos.json',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\videos.json',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\Youtube Compititor '
   'tool\\build\\app\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)

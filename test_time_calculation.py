#!/usr/bin/env python3
"""
Test script to verify the time calculation function works correctly.
"""

from datetime import datetime, timezone, timedelta
import sys
import os

# Add the current directory to the path so we can import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import calculate_time_ago

def test_time_calculations():
    """Test various time calculations."""
    print("Testing time calculation function...")
    
    # Get current date for testing
    now = datetime.now(timezone.utc)
    
    # Test cases: (days_ago, expected_pattern)
    test_cases = [
        (0, "just now"),
        (1, "1 day ago"),
        (7, "1 week ago"),
        (14, "2 weeks ago"),
        (30, "1 month ago"),
        (60, "2 months ago"),
        (365, "1 year ago"),
        (730, "2 years ago")
    ]
    
    print("\nTest Results:")
    print("-" * 50)
    
    for days_ago, expected_pattern in test_cases:
        # Calculate the test date
        test_date = now - timedelta(days=days_ago)
        date_str = test_date.strftime("%Y-%m-%d")
        
        # Get the result from our function
        result = calculate_time_ago(date_str)
        
        # Check if the result contains expected keywords
        success = any(word in result for word in expected_pattern.split())
        status = "✅ PASS" if success else "❌ FAIL"
        
        print(f"{status} | {days_ago:3d} days ago | {date_str} | {result}")
    
    # Test edge cases
    print("\nEdge Case Tests:")
    print("-" * 50)
    
    edge_cases = [
        ("invalid-date", ""),
        ("", ""),
        ("2024-13-45", ""),  # Invalid date
    ]
    
    for test_input, expected in edge_cases:
        result = calculate_time_ago(test_input)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} | Input: '{test_input}' | Output: '{result}'")

if __name__ == "__main__":
    test_time_calculations()

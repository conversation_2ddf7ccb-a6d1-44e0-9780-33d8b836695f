# YouTube Competitor Tool

A personal YouTube video dashboard that displays your curated videos in a card layout similar to YouTube's home page. This tool scrapes video information directly from YouTube without requiring an API key.

## Features

- ✅ **No API Key Required** - Uses web scraping to get video data
- ✅ **YouTube-like Interface** - Card layout matching YouTube's home page design
- ✅ **Complete Video Information** - Shows thumbnails, titles, view counts, channel names, upload dates, and video lengths
- ✅ **Channel Integration** - Displays channel icons and names for each video
- ✅ **Video Length Badges** - Duration overlays on thumbnails (e.g., "3:22")
- ✅ **Enhanced Date Display** - Shows both absolute dates and relative time (e.g., "May 14, 2025 (1 month ago)")
- ✅ **High-Performance Caching** - Lightning-fast loading with local data cache
- ✅ **Smart Data Management** - Scrapes once, stores locally, reads instantly
- ✅ **Bulk Video Adding** - Add multiple videos at once by pasting URLs on separate lines
- ✅ **Powerful Sorting** - Sort by date added, popularity, upload date, title, or channel name
- ✅ **Easy Video Management** - Add/remove videos with intuitive controls
- ✅ **Manual Data Refresh** - User-controlled updates when you want fresh view counts
- ✅ **Data Export** - Download your video collection as CSV or JSON files
- ✅ **Custom Groups** - Create and manage custom video groups/playlists
- ✅ **Advanced Filtering** - Filter videos by groups for focused viewing
- ✅ **Responsive Design** - Works on desktop and mobile devices
- ✅ **Multiple URL Formats** - Supports various YouTube URL formats

## Supported YouTube URL Formats

- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://www.youtube.com/embed/VIDEO_ID`
- `https://www.youtube.com/shorts/VIDEO_ID`

## Installation

1. **Install Python Dependencies**
   ```bash
   pip install Flask requests beautifulsoup4 lxml
   ```

2. **Run the Application**
   ```bash
   python app.py
   ```
   
   Or alternatively:
   ```bash
   flask --app app run --debug
   ```

3. **Open in Browser**
   Navigate to `http://localhost:5000` in your web browser

## How to Use

1. **Add Videos**:
   - **Single Video**: Paste any YouTube video URL in the textarea and click "Add Videos"
   - **Bulk Adding**: Paste multiple YouTube URLs, each on a separate line, for bulk importing

2. **Sort Your Feed**: Use the dropdown menu to sort videos by:
   - Date added (newest/oldest)
   - Most popular (by view count)
   - Upload date (newest/oldest)
   - Title (A-Z)
   - Channel name

3. **View Your Feed**: All your added videos will appear in a grid layout with:
   - High-quality thumbnails with duration badges
   - Video titles (truncated to 2 lines)
   - Channel names and icons
   - Current view counts with precise dates and relative timestamps
   - Direct links to YouTube

4. **Organize with Groups**:
   - Create custom groups using the sidebar form
   - Assign videos to groups using the folder icon on each video card
   - Filter your collection by clicking on group names in the sidebar

5. **Export Your Data**:
   - Click the "Export" button to download your collection
   - Choose CSV format for spreadsheet analysis
   - Choose JSON format for backup or data migration

6. **Refresh Data**: Click "Refresh All Data" to update view counts and other information for all videos
7. **Remove Videos**: Hover over any video card and click the X button to remove it
8. **Keyboard Shortcut**: Press `Ctrl+K` (or `Cmd+K` on Mac) to quickly focus the input field

## Technical Details

### Web Scraping Method
This tool uses multiple scraping techniques to ensure reliability:

1. **Primary Method**: Extracts data from `ytInitialPlayerResponse` JSON object embedded in YouTube pages
2. **Fallback Method**: Parses HTML using BeautifulSoup to find video metadata
3. **Rate Limiting**: Includes delays between requests to avoid being blocked
4. **Error Handling**: Gracefully handles failed requests and missing data

### Enhanced Date Display
The tool provides comprehensive date information for each video:
- **Precise Upload Date**: Extracted from YouTube's meta tags (e.g., "May 14, 2025")
- **Relative Time**: Calculated automatically (e.g., "1 month ago", "3 weeks ago")
- **Combined Display**: Shows both for maximum context: "1.2M views • May 14, 2025 (1 month ago)"
- **Smart Calculation**: Uses precise timestamps to calculate relative time, immune to YouTube's display changes
- **Robust Fallback**: Multiple extraction methods ensure dates are found even when primary sources fail

### Bulk Video Management
Efficiently manage your video collection:
- **Bulk Adding**: Paste multiple YouTube URLs at once, each on a new line
- **Duplicate Prevention**: Automatically prevents adding the same video twice
- **Smart URL Parsing**: Supports all YouTube URL formats in bulk operations
- **Progress Feedback**: Visual confirmation of how many videos were added

### Advanced Sorting Options
Organize your videos exactly how you want:
- **Date Added**: Sort by when you added videos to your collection (newest/oldest)
- **Popularity**: Sort by view count to see your most/least popular videos
- **Upload Date**: Sort by when videos were originally published on YouTube
- **Alphabetical**: Sort by video title for easy browsing
- **Channel**: Group videos by channel name for creator-focused viewing
- **Persistent Selection**: Sorting preference is maintained as you navigate
- **Group-Aware Sorting**: All sorting options work within filtered group views

### Custom Groups & Organization
Powerful content organization system:
- **Create Custom Groups**: Name your groups anything - "Favorites", "Research", "Music", "Tutorials"
- **Multi-Group Assignment**: Videos can belong to multiple groups simultaneously
- **Visual Group Management**: Intuitive modal interface for assigning videos to groups
- **Group Filtering**: Click any group to see only videos in that category
- **Group Statistics**: See video counts for each group at a glance
- **Easy Group Management**: Create, rename, and delete groups with simple controls

### Data Export & Backup
Professional data management capabilities:
- **CSV Export**: Perfect for analysis in Excel, Google Sheets, or data science tools
- **JSON Export**: Complete data backup including all metadata and group assignments
- **Comprehensive Data**: Exports include titles, URLs, view counts, upload dates, groups, and timestamps
- **One-Click Download**: Instant export generation from cached data
- **Data Portability**: Easy migration between systems or sharing with others

### High-Performance Architecture
Revolutionary caching system for blazing-fast performance:
- **"Scrape Once, Store Locally, Read Many Times"** - Core philosophy for maximum efficiency
- **Local Data Cache**: All video information stored in `videos.json` as rich objects
- **Instant Operations**: Viewing, sorting, and removing videos happen instantly (no network requests)
- **Smart Scraping**: Only new videos trigger network requests during adding
- **User-Controlled Refresh**: Manual "Refresh All Data" button for updating view counts when desired
- **Robust Fallback System**: Multiple extraction methods ensure reliable data capture
- **Permanent Date Fix**: Once a video's upload date is found, it's stored forever (no more "Unknown" dates)

### Data Storage
- Complete video objects stored in `videos.json` file (rich cache format)
- Includes all metadata: titles, view counts, upload dates, channel info, thumbnails, groups
- Group definitions and assignments stored in same file for consistency
- No personal data is collected or stored
- Automatic migration from old formats to new rich object format with groups support

### Browser Compatibility
- Modern browsers with JavaScript enabled
- Responsive design works on mobile devices
- Uses Google Fonts and Material Symbols for consistent appearance

## File Structure

```
Youtube Compititor tool/
├── app.py                 # Main Flask application
├── videos.json           # Stored video IDs (created automatically)
├── templates/
│   └── index.html        # HTML template
├── static/
│   └── style.css         # CSS styling
└── README.md            # This file
```

## Troubleshooting

### Videos Not Loading
- Check your internet connection
- YouTube may have changed their page structure (scraping can be fragile)
- Try adding the video again or restart the application

### View Counts Not Updating
- View counts are fetched fresh each time you load the page
- If counts seem outdated, refresh the page
- Some videos may have view counts disabled by the creator

### Application Won't Start
- Ensure all dependencies are installed: `pip install Flask requests beautifulsoup4 lxml`
- Check that port 5000 is not being used by another application
- Try running with `flask --app app run --debug` for more detailed error messages

## Limitations

- **Scraping Dependency**: This tool relies on YouTube's current page structure, which may change
- **Rate Limiting**: Adding many videos at once may be slow due to rate limiting
- **No Authentication**: This is a personal tool and doesn't support user accounts
- **Local Storage**: Videos are stored locally and not synced across devices

## Legal Note

This tool is for personal use only. It scrapes publicly available information from YouTube pages. Please respect YouTube's terms of service and use responsibly.

## Future Enhancements

Potential improvements that could be added:
- Video duration display
- Upload date information
- Channel name and subscriber count
- Search functionality within your saved videos
- Export/import video lists
- Video categories/tags
- Dark/light theme toggle
